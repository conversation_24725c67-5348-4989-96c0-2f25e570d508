import pluginMoego from '@moego/eslint-plugin-moego-fe';
import { globalIgnores } from 'eslint/config';

/** @type {import('eslint').Linter.Config[]} */
export default [
  globalIgnores(['*.config.*', '*.spec.ts', 'src/openApi/', 'src/assets/']),
  { files: ['src/**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  {
    plugins: {
      'moego-fe': pluginMoego,
    },
  },
  ...pluginMoego.configs.recommended.web,
  {
    settings: {
      react: {
        version: '18.2.0',
      },
    },
    rules: {
      'moego-fe/disable-index': [
        'error',
        {
          exclude: ['src/index.tsx'],
        },
      ],
      'moego-fe/disable-export-default': [
        'error',
        {
          exclude: ['*.d.ts'],
        },
      ],
      'react/react-in-jsx-scope': 'off',
      'sonarjs/no-duplicated-branches': 'off',
      '@typescript-eslint/no-namespace': 'off',
    },
  },
];
