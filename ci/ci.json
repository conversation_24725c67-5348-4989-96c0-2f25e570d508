{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-online-booking-client-web", "slack": [], "language": {"type": "node", "version": "18"}, "install": {"commands": ["pnpm i"], "cache_dir": "node_modules"}, "lint": {"commands": ["pnpm i", "bash ci/lint.sh"]}, "build": {"commands": ["bash ci/build.sh"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}], "upload_cdn": [{"from": "build/client/assets", "to": "/w3/obcw/v1/assets", "max_age": 2592000}, {"from": "build/client", "to": "/w3/obcw/v1/", "exclude": "assets/**", "max_age": 0}]}, "deploy": {"type": "service"}, "tia": {"job_name": "Browser<PERSON><PERSON><PERSON> Test (OBC)", "artifact_name": "test-impact-analysis-data-obc"}}