/**
 * 脚本安全配置
 */

// 允许的域名白名单（可以根据需要扩展）
// const ALLOWED_DOMAINS = [
//   'google-analytics.com',
//   'googletagmanager.com',
//   'facebook.net',
//   'connect.facebook.net',
//   'cdn.jsdelivr.net',
//   'unpkg.com',
//   'cdnjs.cloudflare.com',
//   'code.jquery.com',
//   // 可以添加更多受信任的域名
// ];

// 脚本配置限制
export const SCRIPT_LIMITS = {
  MAX_INLINE_SCRIPT_LENGTH: 10000, // 内联脚本最大长度
  MAX_EXTERNAL_SCRIPTS: 10, // 最大外部脚本数量
  MAX_INLINE_SCRIPTS: 5, // 最大内联脚本数量
  MAX_TOTAL_SCRIPTS: 15, // 最大总脚本数量
};

/**
 * 验证脚本URL的安全性
 */
export function validateScriptUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);

    // 只允许HTTPS协议
    if (urlObj.protocol !== 'https:') {
      console.warn('Blocked non-HTTPS script:', url);
      return false;
    }

    // 检查域名是否在白名单中
    // const domain = urlObj.hostname.toLowerCase();
    // const isAllowed = ALLOWED_DOMAINS.some(allowedDomain =>
    //   domain === allowedDomain || domain.endsWith(`.${allowedDomain}`)
    // );

    // if (!isAllowed) {
    //   console.warn('Blocked script from unauthorized domain:', domain);
    //   return false;
    // }

    return true;
  } catch (e) {
    console.warn('Invalid script URL:', url);
    return false;
  }
}

/**
 * 验证内联脚本内容的安全性
 */
export function validateInlineScript(content: string): boolean {
  // 检查脚本长度
  if (content.length > SCRIPT_LIMITS.MAX_INLINE_SCRIPT_LENGTH) {
    console.warn('Inline script too long:', content.length);
    return false;
  }

  // 检查是否包含危险的关键词（可以根据需要扩展）
  const dangerousPatterns = [
    /eval\s*\(/i,
    /Function\s*\(/i,
    /document\.write\s*\(/i,
    /document\.writeln\s*\(/i,
    /innerHTML\s*=/i,
    /outerHTML\s*=/i,
    /<script\b/i,
    /javascript:/i,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(content)) {
      console.warn('Blocked potentially dangerous inline script pattern:', pattern);
      return false;
    }
  }

  return true;
}

/**
 * 验证脚本配置的安全性
 */
export function validateScriptConfig(scriptConfig: any): boolean {
  // 检查必需的字段
  if (!scriptConfig.src && !scriptConfig.content) {
    console.warn('Script config missing both src and content');
    return false;
  }

  // 验证外部脚本
  if (scriptConfig.src) {
    return validateScriptUrl(scriptConfig.src);
  }

  // 验证内联脚本
  if (scriptConfig.content) {
    return validateInlineScript(scriptConfig.content);
  }

  return true;
}

/**
 * 验证脚本列表的安全性
 */
export function validateScriptsList(scripts: any[]): any[] {
  if (!Array.isArray(scripts)) {
    console.warn('Scripts config is not an array');
    return [];
  }

  // 检查数量限制
  if (scripts.length > SCRIPT_LIMITS.MAX_TOTAL_SCRIPTS) {
    console.warn(`Too many scripts: ${scripts.length}, limiting to ${SCRIPT_LIMITS.MAX_TOTAL_SCRIPTS}`);
    scripts = scripts.slice(0, SCRIPT_LIMITS.MAX_TOTAL_SCRIPTS);
  }

  const externalScripts = scripts.filter((s) => s.src);
  const inlineScripts = scripts.filter((s) => s.content);

  if (externalScripts.length > SCRIPT_LIMITS.MAX_EXTERNAL_SCRIPTS) {
    console.warn(
      `Too many external scripts: ${externalScripts.length}, limiting to ${SCRIPT_LIMITS.MAX_EXTERNAL_SCRIPTS}`,
    );
  }

  if (inlineScripts.length > SCRIPT_LIMITS.MAX_INLINE_SCRIPTS) {
    console.warn(`Too many inline scripts: ${inlineScripts.length}, limiting to ${SCRIPT_LIMITS.MAX_INLINE_SCRIPTS}`);
  }

  // 过滤出有效的脚本
  return scripts.filter(validateScriptConfig);
}
