import { createEnum, type EnumValues } from './enum';

// 这里原则上没有实际意义，但是为了声明每个 feature 的作用范围，这里还是跟 metadata 一样，做一下定义，后期维护也比较直观
export const GrowthBookOwnerType = createEnum({
  // 整个系统
  System: [0, 'System'],
  // 企业例如：xxx 宠物企业（包含所有 xxx 公司）
  Enterprise: [1, 'Enterprise'],
  // 公司例如：xxx 宠物公司
  Company: [2, 'Company'],
  // 门店例如：xxx 宠物公司下的门店
  Business: [3, 'Business'],
  // 账户例如：xxx 宠物公司下的账户
  Account: [4, 'Account'],
});

export const GrowthBookFeatureList = createEnum({
  EnablePaymentApiV3: [
    'enable_payment_api_v3',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableBoardingOnlyDeposit: [
    'enable_boarding_only_deposit',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableObClientPortalEditAppt: [
    'enable_ob_client_portal_edit_appt',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  // lead 1.0 版本的白名单
  EnableLeadManagementV1: [
    'leads_management_whitelist_ob',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableOBSellPackage: [
    'enable_ob_sell_package',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableOBNewClientPetCofEditFlow: [
    'ob_new_client_pet_cof_edit_flow',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  OBEnableACHCrud: [
    'ob_enable_ach_crud',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  OBEnableACHPayment: [
    'ob_enable_ach_payment',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableEnterpriseEvaluationRequirements: [
    'enterprise_evaluation_requirements',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnterpriseEvaluationLabel: [
    'enterprise_evaluation_label',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EditUserProfile: [
    'edit_obc_user_profile',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  // 启用首次Daycare免费 目前only for K9
  EnableFirstDaycareFree: [
    'enable_first_daycare_free',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableOBAgreement: [
    'enable_ob_agreement',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableCredit: [
    'credit',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableClientPortalSignup: [
    'enable_client_portal_signup',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  // 暂时只有K9用
  EnableTourFeature: [
    'enable_tour_feature',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  // 临时的一个 key，等 Lydia 跟用户 confirm 完，就可以写死到代码里
  AgreementAlertModalText: [
    'ob_agreement_alert_modal_text',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableAutosetEvaluationDate: [
    'autoset_evaluation_date',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  OverrideStyle: [
    'enterprise_override_style',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableClientTodo: [
    'enable_client_todo',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableBoardingAddOn: [
    'enable_boarding_add_on',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  I18nNS: [
    'obc_i18n_ns',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  DisablePortalVaccineUpdate: [
    'disable_portal_vaccine_update',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  NewOrderV4Flow: ['new_order_flow', { ownerType: GrowthBookOwnerType.Company }],
  DisplayPackageDescription: [
    'obc_display_package_desc',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableMembership: [
    'enable_membership',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  OverrideConfigNamespace: [
    'obc_override_config_namespace',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  // 背景：https://moegoworkspace.slack.com/archives/C08REKP9ELW/p1751593081880299
  EnableOBApptReschedule: [
    'enable_ob_appt_reschedule',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  // 扩展后，不仅仅是 landing page 影响
  OBCOnlyShowCategoryName: [
    'obc_landing_page_only_show_category_name',
    {
      ownerType: GrowthBookOwnerType.Business,
    },
  ],
  OBNewClientPortalForGrooming: [
    'obc_new_client_portal_for_grooming',
    {
      ownerType: GrowthBookOwnerType.Business,
    },
  ],
});

export type GrowthBookFeatureKey = EnumValues<typeof GrowthBookFeatureList>;
