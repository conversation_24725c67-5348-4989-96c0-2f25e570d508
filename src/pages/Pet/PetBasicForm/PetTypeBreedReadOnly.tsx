import { useAtomValue } from 'jotai';
import { memo, useMemo } from 'react';
import { bookingPetOptionsState } from 'state/question/state';
import { type PetInfoType } from 'types/pet';
import { PetReadonly } from './PetReadonly';

interface PetReadonlyComponentProps {
  defaultValues?: Partial<PetInfoType>;
}
function PetTypeBreedReadOnlyComp({ defaultValues }: PetReadonlyComponentProps) {
  const bookingPetOptions = useAtomValue(bookingPetOptionsState);

  const FormReadonlyList = useMemo(() => {
    const { petTypeList = [] } = bookingPetOptions?.data ?? {};
    return [
      { label: 'Type', value: petTypeList.find((pet) => pet.petTypeId === defaultValues?.petTypeId)?.typeName || '' },
      { label: 'Breed', value: defaultValues?.breed },
    ];
  }, [defaultValues, bookingPetOptions]);

  return (
    <>
      {FormReadonlyList.map((item, index) => (
        <PetReadonly key={index} label={item.label} value={item.value} />
      ))}
    </>
  );
}

export const PetTypeBreedReadOnly = memo(PetTypeBreedReadOnlyComp);
