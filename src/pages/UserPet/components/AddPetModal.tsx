import { OperationSection, type OperationSectionProps } from 'components/Layout/OperationSection';
import { usePetInfoForm, type ConfirmPetParams } from 'hooks/usePetInfoForm';
import { PetFullForm } from 'pages/Pet/PetFullForm/PetFullForm';
import { memo, type PropsWithChildren } from 'react';
import { Button } from 'widgets/Button/Button';

interface AddPetModalProps extends Omit<OperationSectionProps, 'onClose'> {
  onClose: (res?: number) => void;
  onConfirm: (params: ConfirmPetParams) => Promise<void>;
  isLoading?: boolean;
}

export const AddPetModal = memo((props: PropsWithChildren<AddPetModalProps>) => {
  const { show, children, onClose, onConfirm, isLoading } = props;
  const { inputsRef, petTypeIdRef, typeSelectorRef, additionalFormRef, medicalFormRef, vaccineListRef, onSubmit } =
    usePetInfoForm();

  return (
    <>
      <OperationSection
        show={show}
        className="z-[1] max-h-[90vh] web:mx-[var(--web-mx)] !pt-0"
        transitionDelay={0}
        portal
        mask
        headerSticky
        title="Add a pet"
        onClose={() => onClose()}
      >
        <div className="flex flex-col text-left mx-[-20px] web:mx-[-32px] mb-[100px] mt-[-20px]">
          <PetFullForm
            basicFormRefs={{
              inputsRef,
              petTypeIdRef,
              typeSelectorRef,
            }}
            hideDefaultVaccineValue
            hideCustomizeQuestion
            additionalFormRef={additionalFormRef}
            medicalFormRef={medicalFormRef}
            vaccineListRef={vaccineListRef}
            isExistingPet={false}
          />
          {children}
        </div>
      </OperationSection>

      <OperationSection
        portal
        show={show}
        className="z-[1] bg-opacity-0 !pt-0 web:mx-[var(--web-mx)] pointer-events-none child:pointer-events-auto"
        transitionDelay={150}
      >
        <Button
          loading={isLoading}
          className="btn-moe-large btn btn-primary w-full"
          onClick={async () => {
            await onSubmit(async (newVal) => {
              onConfirm(newVal);
            });
          }}
        >
          Add pet
        </Button>
      </OperationSection>
    </>
  );
});
