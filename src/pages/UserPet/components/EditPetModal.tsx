import { OperationSection, type OperationSectionProps } from 'components/Layout/OperationSection';
import { usePetInfoForm, type ConfirmPetParams } from 'hooks/usePetInfoForm';
import { useGetPetInfo } from 'pages/Pet/hooks/usePet';
import { PetFullForm } from 'pages/Pet/PetFullForm/PetFullForm';
import { memo } from 'react';
import { Button } from 'widgets/Button/Button';

interface EditPetModalProps extends OperationSectionProps {
  petId: number;
  onConfirm: (newVal: ConfirmPetParams) => Promise<void>;
  defaultValues: {
    basic: Record<string, any>;
    additional: Record<string, any>;
  };
  hideMedicalQuestion?: boolean;
  hideGeneralTitle?: boolean;
}

export const EditPetModal = memo((props: EditPetModalProps) => {
  const { petId, onClose, onConfirm, defaultValues, hideMedicalQuestion, hideGeneralTitle } = props;
  const show = Boolean(petId);
  const getPetInfo = useGetPetInfo();
  const currentPetInfo = getPetInfo(petId);
  const isExistingPet = !currentPetInfo?.isNewPet;

  const { inputsRef, petTypeIdRef, typeSelectorRef, additionalFormRef, medicalFormRef, vaccineListRef, onSubmit } =
    usePetInfoForm();

  return (
    <>
      <OperationSection
        show={show}
        className="z-[1] max-h-[90vh] web:mx-[var(--web-mx)] !pt-0"
        transitionDelay={0}
        portal
        mask
        headerSticky
        title="Edit pet details"
        onClose={onClose}
      >
        <div className="flex flex-col text-left mx-[-20px] web:mx-[-32px] mb-[100px] mt-[-20px]">
          <PetFullForm
            basicFormRefs={{
              inputsRef,
              petTypeIdRef,
              typeSelectorRef,
            }}
            hideCustomizeQuestion
            hideVaccineQuestion
            basicFormDefaultValues={defaultValues.basic}
            additionalFormRef={additionalFormRef}
            additionalFormDefaultValues={defaultValues.additional}
            medicalFormRef={medicalFormRef}
            vaccineListRef={vaccineListRef}
            disabledBasic={isExistingPet}
            isExistingPet={isExistingPet}
            hideMedicalQuestion={hideMedicalQuestion}
            hideGeneralTitle={hideGeneralTitle}
          />
        </div>
      </OperationSection>

      <OperationSection
        portal
        show={show}
        className="z-[1] bg-opacity-0 !pt-0 web:mx-[var(--web-mx)] pointer-events-none child:pointer-events-auto"
        transitionDelay={150}
      >
        <Button
          className="btn-moe-large btn btn-primary w-full"
          onClick={async () => {
            await onSubmit(async (newVal) => {
              await onConfirm(newVal);
            });
          }}
        >
          Save
        </Button>
      </OperationSection>
    </>
  );
});
