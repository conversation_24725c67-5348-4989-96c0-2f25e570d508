import { type BusinessCustomerPetModel } from '@moego/api-web/moego/models/business_customer/v1/business_customer_pet_models';
import { useLatestCallback, useSerialCallback } from '@moego/finance-utils';
import { MoeInput } from 'components/Form/components/MoeInput';
import { OperationSection } from 'components/Layout/OperationSection';
import { forwardRef, Fragment, useImperativeHandle, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { useSetState } from 'react-use';
import { updatePetInfo } from 'state/pet/userPetAtom';
import { PetQuestionKey } from 'types/question';
import { Button } from 'widgets/Button/Button';
import { DefaultTransitions } from 'widgets/Transition/defaults';
import { useGetMedicalInfo } from '../hooks/useGetMedicalInfo';

export interface MedicalInfoEditModalProps {
  onClose?: () => void;
  className?: string;
  isExistingPet: boolean;
  pet: BusinessCustomerPetModel;
}

export interface MedicalInfoEditModalRef {
  show: (props: MedicalInformationFields) => void;
}

interface MedicalInformationFields {
  id: string;
  vetName: string;
  vetPhone: string;
  vetAddress: string;
  healthIssues: string;
}

export const MedicalInfoEditModal = forwardRef<MedicalInfoEditModalRef, MedicalInfoEditModalProps>((props, ref) => {
  const { onClose, isExistingPet, pet } = props;
  const [visible, setVisible] = useState(false);
  const [formInitialStateMap, setFormInitialStateMap] = useSetState<MedicalInformationFields>();
  const getMedicalInfo = useGetMedicalInfo(isExistingPet);
  const data = getMedicalInfo(pet);

  const handleInitForm = useLatestCallback((props: MedicalInformationFields) => {
    setFormInitialStateMap(props);
    setVisible(true);
  });

  const handleChange = useLatestCallback((field: keyof MedicalInformationFields, value: string) => {
    setFormInitialStateMap({
      ...formInitialStateMap,
      [field]: value,
    });
  });

  const handleClose = useLatestCallback(() => {
    setVisible(false);
    onClose?.();
  });

  const handleConfirm = useSerialCallback(async () => {
    if (!formInitialStateMap) return;
    await updatePetInfo(Number(formInitialStateMap.id), {
      medicalInfo: {
        vetName: formInitialStateMap.vetName,
        vetPhoneNumber: formInitialStateMap.vetPhone,
        vetAddress: formInitialStateMap.vetAddress,
        healthIssues: formInitialStateMap.healthIssues,
      },
    });
    toast.success('Update success');
    handleClose();
  });

  useImperativeHandle(ref, () => ({
    show: handleInitForm,
  }));

  const medicalMap = useMemo(
    () => ({
      [PetQuestionKey.VetName]: (
        <MoeInput
          placeholder="Enter name here"
          label="Vet name"
          value={formInitialStateMap?.vetName}
          onChange={(value) => handleChange('vetName', value)}
        />
      ),
      [PetQuestionKey.VetPhone]: (
        <MoeInput
          type="number"
          placeholder="Enter phone number here"
          label="Vet phone number"
          value={formInitialStateMap?.vetPhone}
          onChange={(value) => handleChange('vetPhone', value)}
        />
      ),
      [PetQuestionKey.VetAddress]: (
        <MoeInput
          placeholder="Enter address here"
          label="Vet address"
          value={formInitialStateMap?.vetAddress}
          onChange={(value) => handleChange('vetAddress', value)}
        />
      ),
      [PetQuestionKey.HealthIssues]: (
        <MoeInput
          placeholder="Enter health issues here"
          label="Health issues"
          value={formInitialStateMap?.healthIssues}
          onChange={(value) => handleChange('healthIssues', value)}
        />
      ),
    }),
    [formInitialStateMap, handleChange],
  );

  if (!visible) return null;

  return (
    <>
      <OperationSection
        className="max-h-[90vh] web:mx-[var(--web-mx)] !pt-0 flex flex-col"
        title={'Edit medical information'}
        show={!!visible}
        onClose={handleClose}
        transitionDelay={0}
        transition={DefaultTransitions.transitionY}
        clickMaskToClose
        mask
        headerSticky
        portal
      >
        <div className="flex flex-col gap-[24px] overflow-y-auto overflow-x-visible">
          <div className="flex flex-col gap-[16px]">
            {data.map((item) => {
              return <Fragment key={item.key}>{medicalMap[item.key as keyof typeof medicalMap]}</Fragment>;
            })}
          </div>
          <div className="h-[82px] shrink-0" />
        </div>
      </OperationSection>
      <OperationSection
        portal
        show={!!visible}
        className={'z-[1] bg-opacity-0 !pt-0 web:mx-[var(--web-mx)] pointer-events-none child:pointer-events-auto'}
        transitionDelay={150}
      >
        <Button
          className="btn-moe-large btn btn-primary w-full"
          onClick={() => handleConfirm()}
          loading={handleConfirm.isBusy()}
        >
          Save
        </Button>
      </OperationSection>
    </>
  );
});
