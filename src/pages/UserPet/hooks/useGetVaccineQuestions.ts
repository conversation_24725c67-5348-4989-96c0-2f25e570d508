import { useGetPetInfo } from 'pages/Pet/hooks/usePet';
import { usePetEntryAcceptedQuestions } from 'state/question/hooks/usePetEntryAcceptedQuestions';
import { getVaccinePetQuestions } from 'utils/question';

export const useGetVaccineQuestions = (petId: number) => {
  const getPetInfo = useGetPetInfo();
  const isExistingPet = !getPetInfo(petId)?.isNewPet;
  const questions = usePetEntryAcceptedQuestions(isExistingPet);

  return getVaccinePetQuestions(questions);
};
