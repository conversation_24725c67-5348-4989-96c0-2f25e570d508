import { type BusinessCustomerPetModel } from '@moego/api-web/moego/models/business_customer/v1/business_customer_pet_models';
import { PetGender } from '@moego/api-web/moego/models/customer/v1/customer_pet_enums';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { businessPreferenceState } from 'state/business/state';
import { bookingPetOptionsState } from 'state/question/state';
import { PetQuestionKey } from 'types/question';
import { dateMessageToStandardString } from 'utils/date';
import { formatWeightUnit, type WeightUnit } from 'utils/format';

export const useGetQuestionValueMap = () => {
  const { petTypeList = [] } = useAtomValue(bookingPetOptionsState).data ?? {};
  const unitOfWeight = (useAtomValue(businessPreferenceState)?.data?.unitOfWeight as WeightUnit) ?? null;
  const weightSuffix = formatWeightUnit(unitOfWeight);

  const questionsValueMap: Record<string, (pet: BusinessCustomerPetModel) => string | undefined> = useMemo(
    () => ({
      [PetQuestionKey.PetType]: (pet) => petTypeList?.find((item) => item.petTypeId === pet.petType)?.typeName,
      [PetQuestionKey.PetName]: (pet) => pet.petName,
      [PetQuestionKey.Breed]: (pet) => pet.breed,
      [PetQuestionKey.Weight]: (pet) => `${pet.weight} ${weightSuffix}`,
      [PetQuestionKey.CoatType]: (pet) => pet.coatType,
      [PetQuestionKey.Birthday]: (pet) => dateMessageToStandardString(pet.birthday),
      [PetQuestionKey.Gender]: (pet) =>
        pet.gender === PetGender.MALE ? 'Male' : pet.gender === PetGender.FEMALE ? 'Female' : '',
      [PetQuestionKey.Fixed]: (pet) => pet.fixed,
      [PetQuestionKey.Behavior]: (pet) => pet.behavior,
      [PetQuestionKey.VetName]: (pet) => pet.vetName,
      [PetQuestionKey.VetPhone]: (pet) => pet.vetPhoneNumber,
      [PetQuestionKey.VetAddress]: (pet) => pet.vetAddress,
      [PetQuestionKey.HealthIssues]: (pet) => pet.healthIssues,
    }),
    [petTypeList, weightSuffix],
  );

  return questionsValueMap;
};
