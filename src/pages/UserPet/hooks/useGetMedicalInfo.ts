import { type BusinessCustomerPetModel } from '@moego/api-web/moego/models/business_customer/v1/business_customer_pet_models';
import { ExistingPetAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { usePetEntryAcceptedQuestions } from 'state/question/hooks/usePetEntryAcceptedQuestions';
import { getMedicalPetQuestions } from 'utils/question';
import { type LabelValuePair } from '../types';
import { useGetQuestionValueMap } from './useGetQuestionValueMap';

export const useGetMedicalInfo = (isExistingPet: boolean) => {
  const questions = usePetEntryAcceptedQuestions(isExistingPet);
  const questionsValueMap = useGetQuestionValueMap();

  return useLatestCallback((pet: BusinessCustomerPetModel): LabelValuePair[] => {
    if (!pet) {
      return [];
    }

    const medicalQuestions = getMedicalPetQuestions(questions);

    return medicalQuestions.map((question) => {
      const value = questionsValueMap?.[question.key]?.(pet);

      return {
        label: question.question,
        value: value || '',
        isReadOnly: isExistingPet && question.existingPetAccessMode === ExistingPetAccessMode.VIEW,
        key: question.key,
      };
    });
  });
};
