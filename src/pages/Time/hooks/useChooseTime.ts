import { useTime } from '@moego/client-lib-widgets/dist/CalendarPicker/useTime';
import { useAtomValue } from 'jotai';
import { businessFarthestDayState } from 'state/business/state';

export function useChooseTime() {
  const farthestDayCount = useAtomValue(businessFarthestDayState);

  return useTime({
    canNext: ({ current, now }) => {
      const farthestYear = Math.max(1, Math.ceil(farthestDayCount / 365));

      return (
        (current.year() < now.year + farthestYear ||
          (current.year() === now.year + farthestYear && current.month() < now.month)) &&
        current.isBefore(now.now.add(farthestDayCount, 'day'), 'month')
      );
    },
  });
}
