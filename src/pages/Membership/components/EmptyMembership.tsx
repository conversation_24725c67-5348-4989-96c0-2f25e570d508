import { UniversalEmptyIcon, UniversalEmptyType } from 'components/UniversalEmptyIcon';
import { memo } from 'react';
import { cn } from 'utils/classNames';

export const EmptyMembership = memo(() => {
  return (
    <div className={cn('flex flex-col items-center justify-center gap-[8px] px-[16px] py-[24px]', 'transition-child')}>
      <UniversalEmptyIcon
        emptyType={UniversalEmptyType.PuzzledDog}
        classNames={{
          container: 'w-[120px] h-[120px]',
        }}
      />
      <div className="text-[#202020] text-headline">No available memberships</div>
      <div className="text-[#828282] text-center text-subhead">
        No memberships are available. Please check your pet&apos;s info.
      </div>
    </div>
  );
});
