import { OperationSection, type OperationSectionProps } from 'components/Layout/OperationSection';
import React from 'react';
import { Button } from 'widgets/Button/Button';
import { Icon, IconType } from 'widgets/Icon';
import { DefaultTransitions } from 'widgets/Transition/defaults';
import { useGroomingAvailabilityContext } from '../AppointmentDetail/modules/AppointmentDetailEdit/hooks/useGroomingAvailabilityContext';

export interface UnavailableServiceInfo {
  petId: string;
  petName: string;
  serviceName: string;
  serviceDetailKey: string;
}

export interface PartialNoAvailabilityModalProps extends Pick<OperationSectionProps, 'maskClassName'> {
  show?: boolean;
  onClose?: () => void;
  onPrev?: () => void;
  onNext?: () => void;
  // unavailableServices 现在从 context 获取，不再需要作为参数传递
}

export const PartialNoAvailabilityModal: React.FC<PartialNoAvailabilityModalProps> = (props) => {
  const { show, onClose, onPrev, onNext, maskClassName } = props;

  // 从 context 获取 unavailableServices
  const { getUnavailableServices } = useGroomingAvailabilityContext();
  const unavailableServices = getUnavailableServices();

  // 按 pet 分组显示不可用的服务
  const groupedServices = unavailableServices.reduce((acc, service) => {
    if (!acc[service.petId]) {
      acc[service.petId] = {
        petName: service.petName,
        services: [],
      };
    }
    acc[service.petId].services.push({
      serviceName: service.serviceName,
      serviceDetailKey: service.serviceDetailKey,
    });
    return acc;
  }, {} as Record<string, { petName: string; services: { serviceName: string; serviceDetailKey: string }[] }>);

  return (
    <OperationSection
      className="max-h-[90vh] web:mx-[var(--web-mx)] !pt-0 flex flex-col"
      show={show}
      onClose={onClose}
      transitionDelay={0}
      clickMaskToClose
      transition={DefaultTransitions.transitionY}
      mask
      maskClassName={maskClassName}
      portal
      headerSticky
      title="No availability for some service(s)"
    >
      <div className="text-left">
        <div className="flex items-center py-[16px] bg-[#fff8e5] px-[20px] web:px-[32px] mx-[-20px] web:mx-[-32px]">
          <Icon name={IconType.warningTriangle} className="w-[20px] h-[20px] flex-shrink-0" />
          <div className="text-text-primary ml-[8px]">
            {`The following additional service(s) aren't available in the selected date range and `}
            <span className="font-bold">will be removed</span>
            {`. To keep them, please go back and change your dates.`}
          </div>
        </div>

        <div className="space-y-[16px] my-[12px] px-[12px]">
          {Object.entries(groupedServices).map(([petId, { petName, services }]) => (
            <div key={petId}>
              <div className="text-subhead text-text-secondary mb-[4px]">Removed services for {petName}:</div>
              <ul className="space-y-[4px]">
                {services.map((service, index) => (
                  <li key={index} className="text-headline text-text-primary pl-[10px] list-disc list-inside">
                    {service.serviceName}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      <div className="flex gap-[12px] mt-[20px]">
        <Button className="btn-moe-large btn btn-outline w-[50%] flex-shrink border-[#e6e6e6]" onClick={onPrev}>
          Go back
        </Button>
        <Button className="btn-moe-large btn btn-primary w-[50%] flex-shrink" onClick={onNext}>
          Next
        </Button>
      </div>
    </OperationSection>
  );
};
