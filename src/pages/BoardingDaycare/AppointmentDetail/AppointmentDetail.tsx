import { type GetAppointmentDetailResultAppointmentItem } from '@moego/api-web/moego/client/online_booking/v1/appointment_api';
import { withWaitingLoadableWithSkeletonScreen } from '@moego/client-lib-jotai/dist/waitingLoadable';
import { useAtomValue } from 'jotai';
import { memo, type FC } from 'react';
import { bdAppointmentDetailLoadableAtom } from 'state/boardingDaycare/appointmentAtom';
import { metadataLoadableAtom } from 'state/boardingDaycare/metadataAtom';
import { businessPreferenceState } from 'state/business/state';
import { bdMetaQuestionsState } from 'state/question/state';
import { AppointmentType, AppointmentTypeId } from 'types/appointment';
import { getSearchParam } from 'utils/query';
import { BDLayout } from '../BDLayout/BDLayout';
import { groupClientPortalAppointment } from '../utils';
import { AppointmentGroupItem } from './components/AppointmentGroupItem';
import { useGetAppointmentStatus } from './hooks/useGetAppointmentStatus';
import { useIsInAppointmentDetail } from './hooks/useIsInAppointmentDetail';
import { AppointmentDetailEditProvider } from './modules/AppointmentDetailEdit/AppointmentDetailEdit.context';
import { AppointmentDetailEditModals } from './modules/AppointmentDetailEdit/AppointmentDetailEditModals';
import { AppointmentDetailEditModalsProvider } from './modules/AppointmentDetailEdit/AppointmentDetailEditModals.context';
import { AdditionalServiceDatePickerModalProvider } from './modules/AppointmentDetailEdit/components/AdditionalServiceDatesEditModal/AdditionalServiceDatePickerModal.context';
import { RescheduleOrCancelAppt } from './modules/AppointmentDetailEdit/components/RescheduleOrCancelAppt';
import { GroomingAvailabilityProvider } from './modules/AppointmentDetailEdit/hooks/useGroomingAvailabilityContext';

const AppointmentDetailComponent: FC = () => {
  const { data: appointmentDetailData } = useAtomValue(bdAppointmentDetailLoadableAtom);
  const appointment = (appointmentDetailData?.appointment || {}) as GetAppointmentDetailResultAppointmentItem;
  const { appointmentIsCancelled } = useGetAppointmentStatus();
  const apptTypeId = appointmentIsCancelled
    ? AppointmentTypeId.Cancelled
    : (+(getSearchParam('apptTypeId') || 0) as AppointmentTypeId);

  useIsInAppointmentDetail();

  const appointmentGroup = groupClientPortalAppointment(appointment, appointmentDetailData?.petAndServices);

  return (
    <BDLayout pageTitleConfig={{ title: AppointmentType.mapLabels?.[apptTypeId] }}>
      {appointmentGroup.map(({ mainCareType, petAndServices }) => (
        <AppointmentGroupItem
          key={mainCareType}
          mainCareType={mainCareType}
          petAndServices={petAndServices}
          payment={appointmentDetailData?.payment}
        />
      ))}

      <RescheduleOrCancelAppt />
    </BDLayout>
  );
};

// wrap with context providers
const AppointmentDetailWithContext = () => (
  <GroomingAvailabilityProvider>
    <AppointmentDetailEditProvider>
      <AdditionalServiceDatePickerModalProvider>
        <AppointmentDetailEditModalsProvider>
          <AppointmentDetailComponent />
          <AppointmentDetailEditModals />
        </AppointmentDetailEditModalsProvider>
      </AdditionalServiceDatePickerModalProvider>
    </AppointmentDetailEditProvider>
  </GroomingAvailabilityProvider>
);

export const AppointmentDetail = memo(
  withWaitingLoadableWithSkeletonScreen(
    {},
    metadataLoadableAtom,
    businessPreferenceState,
    bdAppointmentDetailLoadableAtom,
    bdMetaQuestionsState,
  )(AppointmentDetailWithContext),
);
