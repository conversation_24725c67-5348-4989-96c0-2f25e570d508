import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceType } from '@moego/api-web/moego/models/grooming/v1/service_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDateFormatter } from 'hooks/useDateFormat';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import React, { useMemo } from 'react';
import { BookingServiceItemTypeIconMap } from 'types/book';
import { cn } from 'utils/classNames';
import { Condition } from 'widgets/Condition';
import { Icon, IconType } from 'widgets/Icon';
import { Switch } from 'widgets/SwitchCase';
import { extractServiceDetailKey } from '../../AppointmentDetailEdit.utils';

import { useAdditionalPetAndServiceDetail } from '../../hooks/useAdditionalPetAndServiceDetail';
import { getPetDetailDateTypeLabel } from '../AdditionalServiceDatesEditModal/petDetailDateType.enum';

export const AdditionalServiceInfo = () => {
  const appointmentDetail = useAppointmentDetail();
  // 现在从 context 获取 removedServiceDetailKeys，不再需要作为参数传递
  const { additionalPetAndServiceDetailList } = useAdditionalPetAndServiceDetail();
  const flattedDetailList = useMemo(
    () => additionalPetAndServiceDetailList?.map((item) => item.detailList).flat(1) || [],
    [additionalPetAndServiceDetailList],
  );
  const isMultiPet = additionalPetAndServiceDetailList?.length > 1;
  const format = useDateFormatter();

  const serviceItemType = appointmentDetail?.appointment?.mainCareType;
  const isBoarding = serviceItemType === ServiceItemType.BOARDING;
  if (!isBoarding || !flattedDetailList.length) return null;

  return (
    <div className="p-[20px] rounded-[20px] bg-[#fafafa] flex flex-col gap-[16px] text-left">
      {additionalPetAndServiceDetailList?.map((petDetail, index) => {
        // Only show pets that have services to display
        if (!petDetail.detailList.length) {
          return null;
        }

        return (
          <React.Fragment key={petDetail.petId}>
            <div className={cn(['text-[14px] leading-[18px] text-[#333]', { 'mt-[32px]': index > 0 }])}>
              Additional service date<Condition if={isMultiPet}> for {petDetail.petName}:</Condition>
            </div>
            {petDetail.detailList.map((detail) => {
              const { serviceType } = extractServiceDetailKey(detail.serviceDetailKey);
              const careType = detail.careType;
              const iconKey =
                serviceType === ServiceType.ADD_ONS
                  ? IconType.majorAddOnOutlined
                  : BookingServiceItemTypeIconMap.mapLabels[careType];
              const dateList = detail.specificDates || [];
              return (
                <div key={detail.serviceDetailKey} className="flex gap-[12px]">
                  <Icon className="w-[24px] h-[24px]" name={iconKey} />
                  <div className="flex flex-col gap-[4px]">
                    <div className="text-[16px] leading-[22px] text-[#333] font-bold">{detail.serviceName}</div>
                    <Switch shortCircuit>
                      <Switch.Case
                        if={
                          detail.dateType &&
                          [
                            PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
                            PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                            PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY,
                            PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY,
                            PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
                          ].includes(detail.dateType)
                        }
                      >
                        <div className="text-[14px] leading-[18px] text-[#333]">
                          {getPetDetailDateTypeLabel({ dateType: detail.dateType!, serviceItemType })}
                        </div>
                      </Switch.Case>
                      <Switch.Case else>
                        {dateList?.map((date) => (
                          <div key={date} className="text-[14px] leading-[18px] text-[#333]">
                            {format(date)}
                          </div>
                        ))}
                      </Switch.Case>
                    </Switch>
                  </div>
                </div>
              );
            })}
          </React.Fragment>
        );
      })}
    </div>
  );
};
