// 源自 src/pages/BoardingDaycare/ChooseService/dateType.utils.ts

/**
 * PetDetailDateType 相关的处理
 */

import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';

/**
 * Multi Day Daycare 的 isEveryDay 借用了 Appointment 的 dateType = 1 来走，这里单独定义一个变量
 */
export const PET_DETAIL_DATE_TYPE_DAYCARE_EVERY_DAY = PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;

/**
 * Multi Day Daycare - non-require staff addOns 的 dates 借用了 Appointment 的 specificDates 来走，这里单独定义一个变量
 */
export const PET_DETAIL_DATE_TYPE_DAYCARE_SPECIFIC_DATE = PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;

/**
 * Single / Multi Day Daycare - require staff service / addOns 的 dates，借用 DateType.DATE_POINT 来走
 */
export const PET_DETAIL_DATE_TYPE_DAYCARE_DATE_POINT = PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;

export class PetDetailDateTypeUtils {
  /**
   * DatePoint 列表类型（一个或多个），require staff + 多个指定的日期，会涉及到 detail 的 merge 和 detail 的拆分逻辑
   */
  static isDatePointList(dateType?: PetDetailDateType): boolean {
    return dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
  }

  /**
   * require staff + 单个不指定的日期，主要处理兼容性 6/6 会拿掉
   */
  static isFirstDayOrLastDay(dateType?: PetDetailDateType): boolean {
    return (
      dateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY ||
      dateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY
    );
  }

  static isDaycareDatePoint(dateType?: PetDetailDateType): boolean {
    return dateType === PET_DETAIL_DATE_TYPE_DAYCARE_DATE_POINT;
  }

  static isDaycareEveryDay(dateType?: PetDetailDateType): boolean {
    return dateType === PET_DETAIL_DATE_TYPE_DAYCARE_EVERY_DAY;
  }

  static isDaycareSpecificDate(dateType?: PetDetailDateType): boolean {
    return dateType === PET_DETAIL_DATE_TYPE_DAYCARE_SPECIFIC_DATE;
  }
}
