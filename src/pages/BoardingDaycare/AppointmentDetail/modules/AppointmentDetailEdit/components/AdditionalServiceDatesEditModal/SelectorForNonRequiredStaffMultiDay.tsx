import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useInput } from '@moego/client-lib-widgets/Input/utils';
import { Radio } from 'components/Layout/Radio/Radio';
import { RadioGroup } from 'components/Layout/Radio/RadioGroup';
import { RadioFooter } from 'pages/BoardingDaycare/ChooseService/AvailableUIAddOnList/UIAddOnItem/RadioFooter';
import { CounterInput } from 'pages/BoardingDaycare/ChooseService/CounterInput';
import { memo, useMemo } from 'react';
import { cn } from 'utils/classNames';
import { Condition } from 'widgets/Condition';
import { useAppointmentDetailEdit } from '../../AppointmentDetailEdit.context';
import { useAdditionalServiceDatePickerModal } from './AdditionalServiceDatePickerModal.context';
import { getPetDetailDateTypeLabel } from './petDetailDateType.enum';
import {
  PET_DETAIL_DATE_TYPE_DAYCARE_EVERY_DAY,
  PET_DETAIL_DATE_TYPE_DAYCARE_SPECIFIC_DATE,
} from './petDetailDateType.utils';
import { type PetServiceDetailItemInfo } from './types';

// 目前只支持 main service 为 Boarding 的 appointment 进行 detailed reschedule，后面要支持 Daycare 的时候再拿对应数据源
const MainServiceCareType: ServiceItemType = ServiceItemType.BOARDING;
const DefaultQuantityPerDay = 1;

export interface SelectorForNonRequiredStaffMultiDayProps {
  serviceDetailInfo: PetServiceDetailItemInfo;
}

export const SelectorForNonRequiredStaffMultiDay = memo<SelectorForNonRequiredStaffMultiDayProps>((props) => {
  const { serviceDetailInfo } = props;
  const { serviceDetailKey } = serviceDetailInfo;

  // store related
  const { additionalServiceDetailMap = {}, updateAdditionalServiceDetail } = useAppointmentDetailEdit();
  const currentAdditionalServiceDetail = additionalServiceDetailMap[serviceDetailKey];
  const {
    dateType: currentDateType,
    specificDates,
    quantityPerDay,
    serviceName,
  } = useMemo(
    () => ({
      ...serviceDetailInfo,
      ...currentAdditionalServiceDetail,
    }),
    [currentAdditionalServiceDetail, serviceDetailInfo],
  );

  const { openDatePicker, closeDatePicker } = useAdditionalServiceDatePickerModal();
  const dateTypeOptions = useMemo<PetDetailDateType[]>(() => {
    switch (MainServiceCareType as ServiceItemType) {
      case ServiceItemType.BOARDING:
        return [
          PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
          PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
          PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY,
          PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
          PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
        ];
      case ServiceItemType.DAYCARE:
        return [PET_DETAIL_DATE_TYPE_DAYCARE_EVERY_DAY, PET_DETAIL_DATE_TYPE_DAYCARE_SPECIFIC_DATE];
      // 理论上走不到 default
      default:
        return [];
    }
  }, []);
  const quantityPerDayInput = useInput(`${quantityPerDay || DefaultQuantityPerDay}`);

  const handleDateTypeChange = (newDateType: PetDetailDateType) => {
    if (newDateType !== PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
      updateAdditionalServiceDetail(serviceDetailKey, {
        dateType: newDateType,
        specificDates: [],
      });
    } else {
      updateAdditionalServiceDetail(serviceDetailKey, {
        dateType: newDateType,
      });
    }
  };

  const handleSpecificDatesEdit = () => {
    openDatePicker({
      isSingleMode: false,
      valueMultiple: specificDates,
      onChangeMultiple: (value) => {
        updateAdditionalServiceDetail(serviceDetailKey, {
          dateType: PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
          specificDates: value,
        });
        closeDatePicker();
      },
    });
  };

  const handleQuantityPerDayUpdate = (value: string) => {
    updateAdditionalServiceDetail(serviceDetailKey, {
      quantityPerDay: +value,
    });
  };

  return (
    <div className="rounded-[20px] border border-[#E9ECEF] bg-white overflow-hidden">
      <div className="px-[20px] py-[16px] text-[18px] leading-[24px] font-semibold">{serviceName}</div>
      <div className="bg-[#fafafa] px-[20px] py-[16px]">
        <RadioGroup
          className="flex flex-col gap-y-[16px]"
          value={currentDateType}
          onChange={(e) => {
            const newDateType = +e.target.value as PetDetailDateType;
            if (newDateType === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE && !specificDates?.length) {
              handleSpecificDatesEdit();
              return;
            }
            handleDateTypeChange(newDateType);
          }}
        >
          {dateTypeOptions.map((dateType) => {
            const showFooterDate =
              dateType === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE &&
              !!specificDates?.length &&
              currentDateType === dateType;
            const showFooterQuantity = currentDateType === dateType;
            const showRadioFooter = showFooterDate || showFooterQuantity;
            const renderRadioFooter = () => (
              <RadioFooter showFooterDate={showFooterDate} specificDates={specificDates}>
                <Condition if={showFooterQuantity}>
                  <div
                    className={cn('flex items-center  ml-[12px] mt-[4px]', {
                      'mt-[-8px]': !showFooterDate,
                    })}
                  >
                    <CounterInput
                      className="!ml-[-12px] !mr-[4px]"
                      input={quantityPerDayInput}
                      required
                      minValue={1}
                      maxValue={9}
                      onValueChange={handleQuantityPerDayUpdate}
                    />
                    <div className="text-[#888C96] text-[14px] ">per day</div>
                  </div>
                </Condition>
              </RadioFooter>
            );

            return (
              <Radio
                id={`date-type-${serviceDetailKey}-${dateType}`}
                key={dateType}
                value={dateType}
                labelClassName="flex-1"
                footer={showRadioFooter && renderRadioFooter()}
              >
                <div className="flex justify-between">
                  <div className="text-[#202020] text-[16px] leading-[22px] font-[500] cursor-pointer">
                    {getPetDetailDateTypeLabel({ dateType, serviceItemType: MainServiceCareType })}
                  </div>
                  <Condition if={showFooterDate}>
                    <div className="text-primary cursor-pointer text-[14px]" onClick={handleSpecificDatesEdit}>
                      Edit
                    </div>
                  </Condition>
                </div>
              </Radio>
            );
          })}
        </RadioGroup>
      </div>
    </div>
  );
});
