import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { Radio } from 'components/Layout/Radio/Radio';
import { RadioGroup } from 'components/Layout/Radio/RadioGroup';
import dayjs from 'dayjs';
import { RadioFooter } from 'pages/BoardingDaycare/ChooseService/AvailableUIAddOnList/UIAddOnItem/RadioFooter';
import { memo, useMemo } from 'react';
import { DATE_FORMAT_EXCHANGE } from 'utils/const';
import { Condition } from 'widgets/Condition';
import { useAppointmentDetailEdit } from '../../AppointmentDetailEdit.context';
import { useScheduleDate } from '../../hooks/useGetScheduleDate';
import { useGroomingAvailabilityContext } from '../../hooks/useGroomingAvailabilityContext';
import { useAdditionalServiceDatePickerModal } from './AdditionalServiceDatePickerModal.context';
import { getPetDetailDateTypeLabel } from './petDetailDateType.enum';
import { type PetServiceDetailItemInfo } from './types';

// 目前只支持 main service 为 Boarding 的 appointment 进行 detailed reschedule，后面要支持 Daycare 的时候再拿对应数据源
const MainServiceCareType: ServiceItemType = ServiceItemType.BOARDING;

export interface SelectorForRequiredStaffProps {
  serviceDetailInfo: PetServiceDetailItemInfo;
}

export const SelectorForRequiredStaff = memo<SelectorForRequiredStaffProps>((props) => {
  const { serviceDetailInfo } = props;
  const { serviceDetailKey } = serviceDetailInfo;

  // store related
  const { additionalServiceDetailMap = {}, updateAdditionalServiceDetail } = useAppointmentDetailEdit();
  const currentAdditionalServiceDetail = additionalServiceDetailMap[serviceDetailKey];
  const {
    dateType: currentDateType,
    specificDates,
    serviceName,
  } = useMemo(
    () => ({
      ...serviceDetailInfo,
      ...currentAdditionalServiceDetail,
    }),
    [currentAdditionalServiceDetail, serviceDetailInfo],
  );

  const { openDatePicker, closeDatePicker } = useAdditionalServiceDatePickerModal();
  const scheduleDate = useScheduleDate();
  const { getAvailableDates } = useGroomingAvailabilityContext();

  // get available dates for current service
  const currentAvailableDates = useMemo(
    () => getAvailableDates(serviceDetailKey) || [],
    [getAvailableDates, serviceDetailKey],
  );

  const hasFirstDay = useMemo(() => {
    if (!scheduleDate.startDate) return false;
    if (!currentAvailableDates.length) return true;
    const firstDayStr = dayjs(scheduleDate.startDate).format(DATE_FORMAT_EXCHANGE);
    const isAvailable = currentAvailableDates.includes(firstDayStr);
    return isAvailable;
  }, [currentAvailableDates, scheduleDate.startDate]);

  const hasLastDay = useMemo(() => {
    if (!scheduleDate.endDate) return false;
    if (!currentAvailableDates.length) return true;
    const lastDayStr = dayjs(scheduleDate.endDate).format(DATE_FORMAT_EXCHANGE);
    const isAvailable = currentAvailableDates.includes(lastDayStr);
    return isAvailable;
  }, [currentAvailableDates, scheduleDate.endDate]);

  const dateTypeOptions = useMemo<PetDetailDateType[]>(() => {
    switch (MainServiceCareType as ServiceItemType) {
      case ServiceItemType.BOARDING:
        return [
          PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY,
          PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
          PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
        ];
      case ServiceItemType.DAYCARE:
        return [PetDetailDateType.PET_DETAIL_DATE_DATE_POINT];
      default:
        return [];
    }
  }, []);

  /**
   * 直接过滤掉不 available 的 date type options，
   * 不需要在搞额外的校验，依赖第一次选中时拉取的 available dates（对应 currentAvailableDates）
   */
  const availableDateTypeOptions = useMemo(() => {
    return dateTypeOptions.filter((option) => {
      if (option === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY) {
        return hasFirstDay;
      }
      if (option === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY) {
        return hasLastDay;
      }
      return true;
    });
  }, [dateTypeOptions, hasFirstDay, hasLastDay]);

  const handleDateTypeChange = (newDateType: PetDetailDateType) => {
    if (newDateType !== PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
      updateAdditionalServiceDetail(serviceDetailKey, {
        dateType: newDateType,
        specificDates: [],
      });
    } else {
      updateAdditionalServiceDetail(serviceDetailKey, {
        dateType: newDateType,
      });
    }
  };

  const handleSpecificDatesEdit = useLatestCallback(() => {
    openDatePicker({
      /**
       * 关于为什么只能选单天：https://moegoworkspace.slack.com/archives/C08REKP9ELW/p1749117436723129
       */
      isSingleMode: true,
      valueSingle: specificDates?.[0],
      availableDates: currentAvailableDates,
      onChangeSingle: (value) => {
        updateAdditionalServiceDetail(serviceDetailKey, {
          /**
           * TODO: 需要概念清晰的改造 ref: https://moego.atlassian.net/browse/MER-3159
           * 后端改造后，specificDates 的 date type 和 date point 的 date type 不再混淆，所以可以做覆盖，但后续仍需要改造 dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
           */
          dateType: PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
          specificDates: [value],
        });
        closeDatePicker();
      },
    });
  });

  return (
    <div className="rounded-[20px] border border-[#E9ECEF] bg-white overflow-hidden">
      <div className="px-[20px] py-[16px] text-[18px] leading-[24px] font-semibold">{serviceName}</div>
      <div className="bg-[#fafafa] px-[20px] py-[16px]">
        <RadioGroup
          className="flex flex-col gap-y-[16px]"
          value={currentDateType}
          onChange={(e) => {
            const newDateType = +e.target.value as PetDetailDateType;
            if (newDateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT && !specificDates?.length) {
              handleSpecificDatesEdit();
              return;
            }
            // 直接换 DateType，注意 Availability check
            handleDateTypeChange(newDateType);
          }}
        >
          {availableDateTypeOptions.map((dateType) => {
            const showFooterDate =
              dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT &&
              specificDates?.length &&
              currentDateType === dateType;
            const showRadioFooter = showFooterDate;
            return (
              <Radio
                id={`date-type-${serviceDetailKey}-${dateType}`}
                key={dateType}
                value={dateType}
                labelClassName="flex-1"
                footer={
                  showRadioFooter && <RadioFooter showFooterDate={showFooterDate} specificDates={specificDates} />
                }
              >
                <div className="flex justify-between">
                  <div className="text-[#202020] text-[16px] leading-[22px] font-[500] cursor-pointer">
                    {getPetDetailDateTypeLabel({ dateType, serviceItemType: MainServiceCareType })}
                  </div>
                  <Condition if={showFooterDate}>
                    <div className="text-primary cursor-pointer text-[14px]" onClick={handleSpecificDatesEdit}>
                      Edit
                    </div>
                  </Condition>
                </div>
              </Radio>
            );
          })}
        </RadioGroup>
      </div>
    </div>
  );
});
