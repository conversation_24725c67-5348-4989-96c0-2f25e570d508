import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { OperationSection } from 'components/Layout/OperationSection';
import { useEffect, useMemo, useRef } from 'react';
import { cn } from 'utils/classNames';
import { Button } from 'widgets/Button/Button';
import { Condition } from 'widgets/Condition';
import { Switch } from 'widgets/SwitchCase';
import { DefaultTransitions } from 'widgets/Transition/defaults';
import { useAppointmentDetail } from '../../../../hooks/useAppointmentDetail';
import { useAppointmentDetailEdit } from '../../AppointmentDetailEdit.context';
import { useAppointmentDetailEditModals } from '../../AppointmentDetailEditModals.context';
import { maskClassName } from '../../AppointmentDetailEditModals.utils';
import { useAdditionalPetAndServiceDetail } from '../../hooks/useAdditionalPetAndServiceDetail';
import { GroomingAvailabilityProvider } from '../../hooks/useGroomingAvailabilityContext';
import { useAdditionalServiceDatePickerModal } from './AdditionalServiceDatePickerModal.context';
import { SelectorForNonRequiredStaffMultiDay } from './SelectorForNonRequiredStaffMultiDay';
import { SelectorForRequiredStaff } from './SelectorForRequiredStaff';

export interface AdditionalServiceDatesEditModalProps {
  show?: boolean;
  onClose?: () => void;
  onPrev?: () => void;
  onNext?: () => void;
}

export const AdditionalServiceDatesEditModal: React.FC<AdditionalServiceDatesEditModalProps> = (props) => {
  const { show, onClose, onNext, onPrev } = props;
  const appointmentDetail = useAppointmentDetail();
  const { additionalServiceDetailChanged, additionalServiceDetailMap, updateAdditionalServiceDetail } =
    useAppointmentDetailEdit();
  const { additionalServiceDatesEditModal } = useAppointmentDetailEditModals();
  const { removedServiceDetailKeys } = additionalServiceDatesEditModal;
  const { additionalPetAndServiceDetailList, additionalPetAndServiceDetailMap } =
    useAdditionalPetAndServiceDetail(removedServiceDetailKeys);

  const isMultiplePet = useMemo(
    () => appointmentDetail?.petAndServices?.length && appointmentDetail.petAndServices.length > 1,
    [appointmentDetail],
  );

  // check next
  const isAvailableToNext = useMemo(() => {
    return additionalPetAndServiceDetailList?.every((item) =>
      item.detailList.every((detail) => {
        if (!detail.dateType) {
          return false;
        }
        // check date point and specific date
        const isNeedDates = [
          PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
          PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
        ].includes(detail.dateType);
        if (isNeedDates) {
          return !!detail.specificDates?.length;
        }
        return true;
      }),
    );
  }, [additionalPetAndServiceDetailList]);

  const handleConfirm = useLatestCallback(() => {
    onNext?.();
  });

  /**
   * 在编辑弹窗的生命周期里，未编辑的状态下，进入 Dates Edit 做一次初始化，清空 certainDate 相关，否则复用用户已有输入
   */
  const handleInitAdditionalServiceEditModal = useLatestCallback(() => {
    if (!additionalServiceDetailChanged) {
      Object.entries(additionalServiceDetailMap).forEach(([key]) => {
        const serviceDetailItem = additionalPetAndServiceDetailMap[key] || {};
        const isNeedDates =
          serviceDetailItem?.dateType &&
          [PetDetailDateType.PET_DETAIL_DATE_DATE_POINT, PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE].includes(
            serviceDetailItem.dateType,
          );
        if (isNeedDates) {
          updateAdditionalServiceDetail(key, {
            dateType: PetDetailDateType.UNSPECIFIED,
            specificDates: [],
          });
        } else {
          /**
           * 理论上其他 dateType 非 specificDate / datePoint 的情况下，不应返回 specificDates 的，但后台在 not require staff 的 add-on 它返回了，
           * 导致 add-on 的日期填写逻辑有问题，这里补充一个防御逻辑，所有的 specificDates 都清空掉，但我不想和真正业务逻辑混淆，导致代码进一步💩上雕花，先这么写吧 =。=
           * 背景：https://moego.atlassian.net/browse/MER-3175
           */
          updateAdditionalServiceDetail(key, {
            specificDates: [],
          });
        }
      });
    }
  });
  useEffect(() => {
    if (show) {
      handleInitAdditionalServiceEditModal();
    }
  }, [show, handleInitAdditionalServiceEditModal]);

  const { datePickerIsShow } = useAdditionalServiceDatePickerModal();
  // 设计说不希望 modal 层叠，date picker 出来就要把 additional service modal 藏起来
  const isShow = show && !datePickerIsShow;

  /**
   * 滚动位置的保存与恢复
   */
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const lastScrollTopRef = useRef(0);
  useEffect(() => {
    if (show) {
      lastScrollTopRef.current = 0;
    }
  }, [show]);

  useEffect(() => {
    if (!scrollContainerRef.current) return;
    if (datePickerIsShow) {
      lastScrollTopRef.current = scrollContainerRef.current.scrollTop || 0;
    }
  }, [datePickerIsShow]);

  const handleBeforeEnter = useLatestCallback(() => {
    if (!scrollContainerRef.current || lastScrollTopRef.current === 0) return;
    scrollContainerRef.current.scrollTop = lastScrollTopRef.current;
  });

  return (
    <>
      <OperationSection
        ref={scrollContainerRef}
        className="max-h-[90vh] web:mx-[var(--web-mx)] !pt-0 flex flex-col"
        show={isShow}
        onClose={onClose}
        transitionDelay={0}
        clickMaskToClose
        transition={DefaultTransitions.transitionY}
        mask
        maskClassName={maskClassName}
        portal
        headerSticky
        goBack={onPrev}
        beforeEnter={handleBeforeEnter}
        title="Dates for additional service"
      >
        <GroomingAvailabilityProvider>
          <div className="pb-[80px]">
            <div className="flex flex-col gap-[32px] text-left">
              <div className="text-[14px] leading-[18px] text-[#888c96] mb-[-16px]">
                Since your overall service date has changed, please re-confirm dates for the following services:
              </div>
              {additionalPetAndServiceDetailList?.map((petDetail) => {
                // Only show pets that have services to display
                if (!petDetail.detailList.length) {
                  return null;
                }

                return (
                  <div key={petDetail.petId} className="flex flex-col gap-[16px]">
                    <Condition if={isMultiplePet}>
                      <div className="text-[16px] leading-[18px]">{petDetail.petName}:</div>
                    </Condition>
                    {petDetail.detailList.map((detailItem) => {
                      const { requireDedicatedStaff } = detailItem;
                      return (
                        <>
                          <Switch shortCircuit>
                            <Switch.Case if={requireDedicatedStaff}>
                              <SelectorForRequiredStaff
                                key={detailItem.serviceDetailKey}
                                serviceDetailInfo={detailItem}
                              />
                            </Switch.Case>
                            {/* 后面如果要拓展 single date daycare 再额外补充 switch case */}
                            <Switch.Case else>
                              <SelectorForNonRequiredStaffMultiDay
                                key={detailItem.serviceDetailKey}
                                serviceDetailInfo={detailItem}
                              />
                            </Switch.Case>
                          </Switch>
                        </>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </GroomingAvailabilityProvider>
      </OperationSection>
      <OperationSection
        portal
        show={isShow}
        className={cn('!pt-0 web:mx-[var(--web-mx)] pointer-events-none child:pointer-events-auto bg-opacity-0')}
        transitionDelay={150}
      >
        <div className="flex justify-center mt-[10px]">
          <Button
            className="btn-moe-large btn btn-primary w-full btn-shadow"
            disabled={!isAvailableToNext}
            onClick={handleConfirm}
          >
            Next
          </Button>
        </div>
      </OperationSection>
    </>
  );
};
