import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  PET_DETAIL_DATE_TYPE_DAYCARE_DATE_POINT,
  PET_DETAIL_DATE_TYPE_DAYCARE_EVERY_DAY,
  PET_DETAIL_DATE_TYPE_DAYCARE_SPECIFIC_DATE,
} from './petDetailDateType.utils';

// 源自 src/pages/BoardingDaycare/ChooseService/dateType.enum.ts

export const getPetDetailDateTypeLabel = ({
  dateType,
  serviceItemType,
}: {
  dateType: PetDetailDateType;
  serviceItemType?: ServiceItemType;
}) => {
  // Multiple Daycare 会复用 dateType=1 来表示 'Every day'，其余两种情况 Boarding 默认逻辑一致，写出来比较清晰一些
  if (serviceItemType === ServiceItemType.DAYCARE) {
    switch (dateType) {
      case PET_DETAIL_DATE_TYPE_DAYCARE_EVERY_DAY:
        return 'Every day';
      case PET_DETAIL_DATE_TYPE_DAYCARE_SPECIFIC_DATE:
      case PET_DETAIL_DATE_TYPE_DAYCARE_DATE_POINT:
        return 'Certain date(s)';
      default:
        break;
    }
  }
  return BoardingPetDetailDateTypeWordingMap[dateType];
};

export const BoardingPetDetailDateTypeWordingMap: Record<PetDetailDateType, string> = {
  [PetDetailDateType.PET_DETAIL_DATE_EVERYDAY]: 'Every day except the check-out day',
  // non-require staff 用的，每条记录有多天
  [PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE]: 'Certain date(s)',
  // require staff 用的，每条记录只有一天，但 UI 上合并为多天来显示
  [PetDetailDateType.PET_DETAIL_DATE_DATE_POINT]: 'Certain date(s)',
  [PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY]: 'Every day',
  [PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY]: 'Every day except the check-in day',
  [PetDetailDateType.PET_DETAIL_DATE_LAST_DAY]: 'Last day',
  [PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY]: 'First day',
  [PetDetailDateType.UNSPECIFIED]: '',
};
