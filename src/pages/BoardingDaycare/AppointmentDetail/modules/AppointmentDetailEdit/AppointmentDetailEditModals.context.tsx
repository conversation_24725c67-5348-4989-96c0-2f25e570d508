import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { createContext, useContext, useMemo, type PropsWithChildren } from 'react';
import { useSetState } from 'react-use';

import { type AlertInfoType } from './components/AlertInfoModal';

interface CommonModalProps {
  show: boolean;
  onClose: () => void;
}

export enum ApptDetailModal {
  EditScheduleDate = 'editScheduleDate',
  EditTimes = 'editTimes',
  NoAvailability = 'noAvailability',
  AlertInfoModal = 'alertInfoModal',
  PreviewChangesModal = 'previewChangesModal',
  AdditionalServiceDatesEditModal = 'additionalServiceDatesEditModal',
  PartialNoAvailability = 'partialNoAvailability',
}

/**
 * 需要注意避免直接引用 Modal 组件的 typing，避免循环引用的问题
 */
interface EditModalsState {
  [ApptDetailModal.EditScheduleDate]: CommonModalProps & {
    onNext?: () => void;
  };
  [ApptDetailModal.EditTimes]: CommonModalProps & {
    onNext?: () => void;
    onPrev?: () => void;
  };
  [ApptDetailModal.NoAvailability]: CommonModalProps;
  [ApptDetailModal.AlertInfoModal]: CommonModalProps & {
    infoType?: AlertInfoType;
  };
  [ApptDetailModal.PreviewChangesModal]: CommonModalProps & {
    onSubmit?: () => Promise<void> | void;
    onPrev?: () => void;
    // removedServiceDetailKeys 现在从 context 获取，不再需要作为参数传递
  };
  [ApptDetailModal.AdditionalServiceDatesEditModal]: CommonModalProps & {
    onNext?: () => void;
    onPrev?: () => void;
    // removedServiceDetailKeys 现在从 context 获取，不再需要作为参数传递
  };
  [ApptDetailModal.PartialNoAvailability]: CommonModalProps & {
    onNext?: () => void;
    onPrev?: () => void;
    // unavailableServices 现在从 context 获取，不再需要作为参数传递
  };
}

const getModalDefaultProps = () => ({
  show: false,
  onClose: () => {},
});

const defaultModalsState = {
  [ApptDetailModal.EditScheduleDate]: getModalDefaultProps(),
  [ApptDetailModal.EditTimes]: getModalDefaultProps(),
  [ApptDetailModal.NoAvailability]: getModalDefaultProps(),
  [ApptDetailModal.AlertInfoModal]: getModalDefaultProps(),
  [ApptDetailModal.PreviewChangesModal]: getModalDefaultProps(),
  [ApptDetailModal.AdditionalServiceDatesEditModal]: getModalDefaultProps(),
  [ApptDetailModal.PartialNoAvailability]: getModalDefaultProps(),
};

interface AppointmentDetailEditModalsContextProps extends EditModalsState {
  closeModal: (key: keyof EditModalsState, shouldDelay?: boolean) => void;
  openModal: <T extends keyof EditModalsState>(key: T, params?: Partial<EditModalsState[T]>) => void;
  setModalState: <T extends keyof EditModalsState>(key: T, params: EditModalsState[T]) => void;
}

export const AppointmentDetailEditModalsContext = createContext({} as AppointmentDetailEditModalsContextProps);

export const useAppointmentDetailEditModals = () => useContext(AppointmentDetailEditModalsContext);

// 如果马上需要打开下一个弹窗，需要延迟一下，避免统一的 mask 消失
const CLOSE_DELAY = 50;

export const AppointmentDetailEditModalsProvider: React.FC<PropsWithChildren> = (props) => {
  const [modalState, setAllModalState] = useSetState<EditModalsState>(defaultModalsState);

  const closeModal = useLatestCallback(async (key: keyof EditModalsState, shouldDelay?: boolean) => {
    if (shouldDelay) {
      await new Promise((resolve) => setTimeout(resolve, CLOSE_DELAY));
    }
    setAllModalState({ [key]: getModalDefaultProps() });
  });

  const openModal = useLatestCallback(
    <T extends keyof EditModalsState>(key: T, params?: Partial<EditModalsState[T]>) => {
      setAllModalState({
        [key]: {
          show: true,
          onClose: () => {
            closeModal(key);
          },
          ...params,
        },
      });
    },
  );

  const setModalState = useLatestCallback(<T extends keyof EditModalsState>(key: T, params: EditModalsState[T]) => {
    setAllModalState({ [key]: params });
  });

  const contextValue = useMemo(() => {
    return {
      ...modalState,
      closeModal,
      openModal,
      setModalState,
    };
  }, [modalState, closeModal, openModal, setModalState]);
  return (
    <AppointmentDetailEditModalsContext.Provider value={contextValue}>
      {props.children}
    </AppointmentDetailEditModalsContext.Provider>
  );
};
