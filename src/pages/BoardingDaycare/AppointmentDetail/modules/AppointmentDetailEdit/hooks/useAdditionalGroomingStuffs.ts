import { type GetAppointmentDetailResultPetItem } from '@moego/api-web/moego/client/online_booking/v1/appointment_api';
import { ServiceType } from '@moego/api-web/moego/models/grooming/v1/service_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import { useMemo } from 'react';
import { isRequireDedicatedStaffCareType } from 'utils/service';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { getApptServiceDetailKey } from '../AppointmentDetailEdit.utils';
import { type PetServiceDetailItemInfo } from '../components/AdditionalServiceDatesEditModal/types';

export interface PetServiceDetailItemInfoWithPetInfo extends PetServiceDetailItemInfo {
  petInfo: GetAppointmentDetailResultPetItem;
}

export const useAdditionalGroomingStuffs = () => {
  const appointmentDetail = useAppointmentDetail();
  const { additionalServiceDetailMap } = useAppointmentDetailEdit();

  const [additionalGroomingPetAndServiceDetailList, additionalGroomingPetAndServiceDetailMap] = useMemo(() => {
    if (!appointmentDetail) return [[], {}];
    const { petAndServices } = appointmentDetail;
    const petDetailList = petAndServices.map((petDetail) => {
      const { pet, services, addOns } = petDetail;
      const filteredServices = services.filter(
        (service) => service.careType === ServiceItemType.GROOMING && isRequireDedicatedStaffCareType(service.careType),
      );
      const filteredAddons = addOns.filter(
        (service) => service.careType === ServiceItemType.GROOMING && service.requireDedicatedStaff,
      );
      const detailList: PetServiceDetailItemInfoWithPetInfo[] = [];
      filteredServices.forEach((detail) => {
        const { petDetailId, careType } = detail;
        const serviceDetailKey = getApptServiceDetailKey(pet.id, petDetailId, careType, ServiceType.SERVICE);
        const data = additionalServiceDetailMap[serviceDetailKey];
        detailList.push({
          petId: pet.id,
          serviceDetailKey,
          requireDedicatedStaff: isRequireDedicatedStaffCareType(careType),
          serviceId: detail.id,
          petInfo: pet,
          ...detail,
          ...data,
        });
      });
      filteredAddons.forEach((detail) => {
        const { petDetailId, careType } = detail;
        const serviceDetailKey = getApptServiceDetailKey(pet.id, petDetailId, careType, ServiceType.ADD_ONS);
        const data = additionalServiceDetailMap[serviceDetailKey];
        detailList.push({
          petId: pet.id,
          serviceDetailKey,
          serviceName: detail.addOnName,
          serviceId: detail.id,
          petInfo: pet,
          ...detail,
          ...data,
        });
      });
      return {
        petId: pet.id,
        petName: pet.petName,
        detailList,
        petInfo: pet,
      };
    });
    const petDetailMap = petDetailList
      .map((item) => item.detailList)
      .flat()
      .reduce((acc, cur) => {
        acc[cur.serviceDetailKey] = cur;
        return acc;
      }, {} as Record<string, PetServiceDetailItemInfoWithPetInfo>);

    return [petDetailList, petDetailMap];
  }, [appointmentDetail, additionalServiceDetailMap]);

  return {
    additionalGroomingPetAndServiceDetailList,
    additionalGroomingPetAndServiceDetailMap,
  };
};
