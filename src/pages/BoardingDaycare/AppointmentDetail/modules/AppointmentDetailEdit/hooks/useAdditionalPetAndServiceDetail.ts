import { ServiceType } from '@moego/api-web/moego/models/grooming/v1/service_enums';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import { useMemo } from 'react';
import { isRequireDedicatedStaffCareType } from 'utils/service';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { getApptServiceDetailKey } from '../AppointmentDetailEdit.utils';
import { type PetServiceDetailItemInfo } from '../components/AdditionalServiceDatesEditModal/types';
import { useGroomingAvailabilityContext } from './useGroomingAvailabilityContext';

export const useAdditionalPetAndServiceDetail = (excludeServiceDetailKeys?: string[]) => {
  const appointmentDetail = useAppointmentDetail();
  const { additionalServiceDetailMap } = useAppointmentDetailEdit();
  const { getRemovedServiceDetailKeys } = useGroomingAvailabilityContext();

  // 如果没有传入 excludeServiceDetailKeys 参数，则从 context 获取
  const actualExcludeServiceDetailKeys = excludeServiceDetailKeys ?? getRemovedServiceDetailKeys();

  const [additionalPetAndServiceDetailList, additionalPetAndServiceDetailMap] = useMemo(() => {
    if (!appointmentDetail) return [[], {}];
    const { appointment, petAndServices } = appointmentDetail;
    const mainCareType = appointment.mainCareType;
    const petDetailList = petAndServices.map((petDetail) => {
      const { pet, services, addOns } = petDetail;
      const filteredServices = services.filter((service) => service.careType !== mainCareType);
      const detailList: PetServiceDetailItemInfo[] = [];
      filteredServices.forEach((detail) => {
        const { petDetailId, careType } = detail;
        const serviceDetailKey = getApptServiceDetailKey(pet.id, petDetailId, careType, ServiceType.SERVICE);

        // Skip if this service should be excluded (unavailable)
        if (actualExcludeServiceDetailKeys.includes(serviceDetailKey)) {
          return;
        }

        const data = additionalServiceDetailMap[serviceDetailKey];
        detailList.push({
          petId: pet.id,
          serviceDetailKey,
          requireDedicatedStaff: isRequireDedicatedStaffCareType(careType),
          serviceId: detail.id,
          ...detail,
          ...data,
        });
      });
      addOns.forEach((detail) => {
        const { petDetailId, careType } = detail;
        const serviceDetailKey = getApptServiceDetailKey(pet.id, petDetailId, careType, ServiceType.ADD_ONS);

        // Skip if this service should be excluded (unavailable)
        if (actualExcludeServiceDetailKeys.includes(serviceDetailKey)) {
          return;
        }

        const data = additionalServiceDetailMap[serviceDetailKey];
        detailList.push({
          petId: pet.id,
          serviceDetailKey,
          serviceName: detail.addOnName,
          serviceId: detail.id,
          ...detail,
          ...data,
        });
      });
      return {
        petId: pet.id,
        petName: pet.petName,
        detailList,
      };
    });
    const petDetailMap = petDetailList
      .map((item) => item.detailList)
      .flat()
      .reduce((acc, cur) => {
        acc[cur.serviceDetailKey] = cur;
        return acc;
      }, {} as Record<string, PetServiceDetailItemInfo>);

    return [petDetailList, petDetailMap];
  }, [appointmentDetail, additionalServiceDetailMap, actualExcludeServiceDetailKeys]);

  return {
    additionalPetAndServiceDetailList,
    additionalPetAndServiceDetailMap,
  };
};
