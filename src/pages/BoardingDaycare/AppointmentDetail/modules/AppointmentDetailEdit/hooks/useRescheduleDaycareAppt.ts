import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { useActionAtom } from '@moego/client-lib-jotai/action';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import toast from 'react-hot-toast';
import {
  refreshBDAppointmentDetailAction,
  updateAppointmentAction,
  updateBookingRequestAction,
} from 'state/boardingDaycare/appointmentAtom';
import { FlowAbortedError } from 'utils/errors';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { ApptDetailModal, useAppointmentDetailEditModals } from '../AppointmentDetailEditModals.context';
import { useCheckAvailability } from './useCheckAvailability';
import { useCheckGroomingServiceAvailability } from './useCheckGroomingServiceAvailability';
import { useGetApptRescheduleSubmitParams } from './useGetApptRescheduleSubmitParams';
import { useGroomingAvailabilityContext } from './useGroomingAvailabilityContext';

export const useRescheduleDaycareAppt = () => {
  const refreshAppointmentDetail = useActionAtom(refreshBDAppointmentDetailAction);
  const { openModal, closeModal } = useAppointmentDetailEditModals();
  const { initApptDetailEditState } = useAppointmentDetailEdit();
  const { getApptRescheduleSubmitParams } = useGetApptRescheduleSubmitParams();
  const updateAppointment = useActionAtom(updateAppointmentAction);
  const updateBookingRequest = useActionAtom(updateBookingRequestAction);
  const { checkAvailability } = useCheckAvailability();
  const { checkAvailability: checkGroomingAvailability } = useCheckGroomingServiceAvailability();
  const appointmentDetailData = useAppointmentDetail();

  // 使用新的 context 来管理共享状态
  const {
    setAvailabilityData,
    getRemovedServiceDetailKeys,
    setRemovedServiceDetailKeys,
    setUnavailableServices,
  } = useGroomingAvailabilityContext();

  const handleSubmitBooking = useLatestCallback(async () => {
    const petAndServices = await getApptRescheduleSubmitParams();
    const isBookingRequest = appointmentDetailData?.appointment?.isBookingRequest;
    const removedServiceDetailKeys = getRemovedServiceDetailKeys();

    if (isBookingRequest) {
      await updateBookingRequest({
        petAndServices,
        removedServiceDetailKeys,
      });
    } else {
      await updateAppointment({
        petAndServices,
        removedServiceDetailKeys,
      });
    }

    await refreshAppointmentDetail();
    toast.success('Successfully updated.');
  });

  const handleEditDate = useLatestCallback(async () => {
    await new Promise<void>((resolve, reject) => {
      openModal(ApptDetailModal.EditScheduleDate, {
        onNext: async () => {
          closeModal(ApptDetailModal.EditScheduleDate, true);
          await handleEditTime();
          resolve();
        },
        onClose: () => {
          closeModal(ApptDetailModal.EditScheduleDate);
          reject(new FlowAbortedError());
        },
      });
    });
  });

  const handleEditTime = useLatestCallback(async () => {
    await new Promise<void | number>((resolve, reject) => {
      openModal(ApptDetailModal.EditTimes, {
        onClose: () => {
          closeModal(ApptDetailModal.EditTimes);
          reject(new FlowAbortedError());
        },
        onNext: async () => {
          closeModal(ApptDetailModal.EditTimes, true);
          await handlePreviewChanges();
          resolve();
        },
        onPrev: async () => {
          closeModal(ApptDetailModal.EditTimes, true);
          await handleEditDate();
          resolve();
        },
      });
    });
  });

  const handlePreviewChanges = useLatestCallback(async () => {
    await new Promise<void | number>((resolve, reject) => {
      openModal(ApptDetailModal.PreviewChangesModal, {
        onClose: () => {
          closeModal(ApptDetailModal.PreviewChangesModal);
          reject(new FlowAbortedError());
        },
        onSubmit: async () => {
          await handleSubmitBooking();
          closeModal(ApptDetailModal.PreviewChangesModal);
          resolve();
        },
        onPrev: async () => {
          closeModal(ApptDetailModal.PreviewChangesModal, true);
          handleEditTime();
          resolve();
        },
      });
    });
  });

  const handleRescheduleDaycareAppt = useLatestCallback(async () => {
    initApptDetailEditState();
    await new Promise<void>((resolve, reject) => {
      openModal(ApptDetailModal.EditScheduleDate, {
        onNext: async () => {
          closeModal(ApptDetailModal.EditScheduleDate, true);
          try {
            const isDateTimeAvailable = await checkAvailability();
            if (!isDateTimeAvailable) {
              openModal(ApptDetailModal.NoAvailability, {
                onClose: () => {
                  closeModal(ApptDetailModal.NoAvailability, true);
                  handleEditDate();
                },
              });
              return reject(new FlowAbortedError());
            }

            // Check grooming service availability
            const groomingAvailabilityResult = await checkGroomingAvailability();
            const { hasUnavailableGroomingServices, petIdMapToUnavailableServiceDetailKeyList } =
              groomingAvailabilityResult;

            // Store the grooming availability data for later use in additional service dates modal
            setAvailabilityData(groomingAvailabilityResult);

            if (hasUnavailableGroomingServices) {
              // Convert to unavailable services info for the modal
              const unavailableServices = Object.entries(petIdMapToUnavailableServiceDetailKeyList).flatMap(
                ([petId, serviceDetailKeys]) => {
                  return serviceDetailKeys.map((serviceDetailKey) => {
                    // Get pet and service info from additionalGroomingPetAndServiceDetailMap
                    const serviceDetail =
                      groomingAvailabilityResult.additionalGroomingPetAndServiceDetailMap?.[serviceDetailKey];
                    return {
                      petId,
                      petName: serviceDetail?.petInfo?.petName || 'Unknown Pet',
                      serviceName: serviceDetail?.serviceName || 'Unknown Service',
                      serviceDetailKey,
                    };
                  });
                },
              );

              // Store the unavailable services and removed service detail keys in context
              setUnavailableServices(unavailableServices);
              const removedServiceDetailKeys = Object.values(petIdMapToUnavailableServiceDetailKeyList).flat();
              setRemovedServiceDetailKeys(removedServiceDetailKeys);

              openModal(ApptDetailModal.PartialNoAvailability, {
                onPrev: () => {
                  closeModal(ApptDetailModal.PartialNoAvailability, true);
                  handleEditDate();
                },
                onNext: async () => {
                  closeModal(ApptDetailModal.PartialNoAvailability, true);
                  // Continue with the flow, the unavailable services will be handled in submit
                  await handleEditTime();
                  resolve();
                },
              });
              return;
            }
          } catch (error) {
            reject(error);
          }

          await handleEditTime();
          resolve();
        },
        onClose: () => {
          closeModal(ApptDetailModal.EditScheduleDate);
          reject(new FlowAbortedError());
        },
      });
    });
  });

  return {
    handleRescheduleDaycareAppt,
  };
};
