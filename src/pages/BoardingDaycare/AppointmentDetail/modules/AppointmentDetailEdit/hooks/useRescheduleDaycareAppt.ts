import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { useActionAtom } from '@moego/client-lib-jotai/action';
import { useSetAtom } from 'jotai';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import toast from 'react-hot-toast';
import {
  refreshBDAppointmentDetailAction,
  updateAppointmentAction,
  updateBookingRequestAction,
} from 'state/boardingDaycare/appointmentAtom';
import { FlowAbortedError } from 'utils/errors';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { ApptDetailModal, useAppointmentDetailEditModals } from '../AppointmentDetailEditModals.context';
import { useCheckAvailability } from './useCheckAvailability';
import { useCheckGroomingServiceAvailability } from './useCheckGroomingServiceAvailability';
import { useGetApptRescheduleSubmitParams } from './useGetApptRescheduleSubmitParams';
import { groomingAvailabilityDataAtom } from './useGroomingAvailabilityContext';

export const useRescheduleDaycareAppt = () => {
  const refreshAppointmentDetail = useActionAtom(refreshBDAppointmentDetailAction);
  const { openModal, closeModal } = useAppointmentDetailEditModals();
  const { initApptDetailEditState } = useAppointmentDetailEdit();
  const { getApptRescheduleSubmitParams } = useGetApptRescheduleSubmitParams();
  const updateAppointment = useActionAtom(updateAppointmentAction);
  const updateBookingRequest = useActionAtom(updateBookingRequestAction);
  const { checkAvailability } = useCheckAvailability();
  const { checkAvailability: checkGroomingAvailability } = useCheckGroomingServiceAvailability();
  const appointmentDetailData = useAppointmentDetail();
  const setGroomingAvailabilityData = useSetAtom(groomingAvailabilityDataAtom);

  const handleSubmitBooking = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    const petAndServices = await getApptRescheduleSubmitParams();
    const isBookingRequest = appointmentDetailData?.appointment?.isBookingRequest;

    if (isBookingRequest) {
      await updateBookingRequest({
        petAndServices,
        removedServiceDetailKeys,
      });
    } else {
      await updateAppointment({
        petAndServices,
        removedServiceDetailKeys,
      });
    }

    await refreshAppointmentDetail();
    toast.success('Successfully updated.');
  });

  const handleEditDate = useLatestCallback(async () => {
    await new Promise<void>((resolve, reject) => {
      openModal(ApptDetailModal.EditScheduleDate, {
        onNext: async () => {
          closeModal(ApptDetailModal.EditScheduleDate, true);
          await handleEditTime();
          resolve();
        },
        onClose: () => {
          closeModal(ApptDetailModal.EditScheduleDate);
          reject(new FlowAbortedError());
        },
      });
    });
  });

  const handleEditTime = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    await new Promise<void | number>((resolve, reject) => {
      openModal(ApptDetailModal.EditTimes, {
        onClose: () => {
          closeModal(ApptDetailModal.EditTimes);
          reject(new FlowAbortedError());
        },
        onNext: async () => {
          closeModal(ApptDetailModal.EditTimes, true);
          await handlePreviewChanges(removedServiceDetailKeys);
          resolve();
        },
        onPrev: async () => {
          closeModal(ApptDetailModal.EditTimes, true);
          await handleEditDate();
          resolve();
        },
      });
    });
  });

  const handlePreviewChanges = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    await new Promise<void | number>((resolve, reject) => {
      openModal(ApptDetailModal.PreviewChangesModal, {
        removedServiceDetailKeys,
        onClose: () => {
          closeModal(ApptDetailModal.PreviewChangesModal);
          reject(new FlowAbortedError());
        },
        onSubmit: async () => {
          await handleSubmitBooking(removedServiceDetailKeys);
          closeModal(ApptDetailModal.PreviewChangesModal);
          resolve();
        },
        onPrev: async () => {
          closeModal(ApptDetailModal.PreviewChangesModal, true);
          handleEditTime(removedServiceDetailKeys);
          resolve();
        },
      });
    });
  });

  const handleRescheduleDaycareAppt = useLatestCallback(async () => {
    initApptDetailEditState();
    await new Promise<void>((resolve, reject) => {
      openModal(ApptDetailModal.EditScheduleDate, {
        onNext: async () => {
          closeModal(ApptDetailModal.EditScheduleDate, true);
          try {
            const isDateTimeAvailable = await checkAvailability();
            if (!isDateTimeAvailable) {
              openModal(ApptDetailModal.NoAvailability, {
                onClose: () => {
                  closeModal(ApptDetailModal.NoAvailability, true);
                  handleEditDate();
                },
              });
              return reject(new FlowAbortedError());
            }

            // Check grooming service availability
            const groomingAvailabilityResult = await checkGroomingAvailability();
            const { hasUnavailableGroomingServices, petIdMapToUnavailableServiceDetailKeyList } =
              groomingAvailabilityResult;

            // Store the grooming availability data for later use in additional service dates modal
            setGroomingAvailabilityData(groomingAvailabilityResult);

            if (hasUnavailableGroomingServices) {
              // Convert to unavailable services info for the modal
              const unavailableServices = Object.entries(petIdMapToUnavailableServiceDetailKeyList).flatMap(
                ([petId, serviceDetailKeys]) => {
                  return serviceDetailKeys.map((serviceDetailKey) => {
                    // Get pet and service info from additionalGroomingPetAndServiceDetailMap
                    const serviceDetail =
                      groomingAvailabilityResult.additionalGroomingPetAndServiceDetailMap?.[serviceDetailKey];
                    return {
                      petId,
                      petName: serviceDetail?.petInfo?.petName || 'Unknown Pet',
                      serviceName: serviceDetail?.serviceName || 'Unknown Service',
                      serviceDetailKey,
                    };
                  });
                },
              );

              openModal(ApptDetailModal.PartialNoAvailability, {
                unavailableServices,
                onPrev: () => {
                  closeModal(ApptDetailModal.PartialNoAvailability, true);
                  handleEditDate();
                },
                onNext: async () => {
                  closeModal(ApptDetailModal.PartialNoAvailability, true);
                  // Store the removed service detail keys for later use in submit
                  const removedServiceDetailKeys = Object.values(petIdMapToUnavailableServiceDetailKeyList).flat();
                  // Continue with the flow, the unavailable services will be handled in submit
                  await handleEditTime(removedServiceDetailKeys);
                  resolve();
                },
              });
              return;
            }
          } catch (error) {
            reject(error);
          }

          await handleEditTime();
          resolve();
        },
        onClose: () => {
          closeModal(ApptDetailModal.EditScheduleDate);
          reject(new FlowAbortedError());
        },
      });
    });
  });

  return {
    handleRescheduleDaycareAppt,
  };
};
