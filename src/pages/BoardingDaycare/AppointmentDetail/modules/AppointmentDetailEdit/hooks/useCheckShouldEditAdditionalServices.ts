import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceType } from '@moego/api-web/moego/models/grooming/v1/service_enums';
import { useLatestCallback } from '@moego/finance-utils';
import dayjs from 'dayjs';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import { getExchangeFormattedDate } from 'utils/date';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { getApptServiceDetailKey } from '../AppointmentDetailEdit.utils';

export const useCheckShouldEditAdditionalServices = () => {
  const appointmentDetailData = useAppointmentDetail();
  const { getApptDetailEditState } = useAppointmentDetailEdit();
  /**
   * 跳转到 Additional Services 的条件：
   * 前面 date 有变更 & 排除被删除的服务后剩下的 add-on 列表里有选择了 certain dates 类型的
   */
  const checkShouldEditAdditionalServices = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    if (!appointmentDetailData?.appointment) throw new Error('Missing appointment data');
    const { appointment, petAndServices } = appointmentDetailData;

    // date changed
    const startDate = appointment.startDate;
    const endDate = appointment.endDate;
    const { scheduleDate } = getApptDetailEditState();
    const isScheduleDateChanged =
      getExchangeFormattedDate(dayjs(scheduleDate.bookingStartDate)) !== startDate ||
      getExchangeFormattedDate(dayjs(scheduleDate.bookingEndDate)) !== endDate;

    // certain dates
    const isHasCertainDates = petAndServices.some((petDetail) => {
      const { pet, services, addOns } = petDetail;
      const filterServices = services.filter((service) => service.careType !== appointment.mainCareType);

      const checkFunc = (detail: (typeof services)[0] | (typeof addOns)[0], serviceType: ServiceType) => {
        const serviceDetailKey = getApptServiceDetailKey(pet.id, detail.petDetailId, detail.careType, serviceType);

        // Skip if this service is being removed
        if (removedServiceDetailKeys?.includes(serviceDetailKey)) {
          return false;
        }

        return (
          detail.dateType === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE ||
          detail.dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT
        );
      };

      return (
        filterServices.some((detail) => checkFunc(detail, ServiceType.SERVICE)) ||
        addOns.some((detail) => checkFunc(detail, ServiceType.ADD_ONS))
      );
    });

    return isScheduleDateChanged && isHasCertainDates;
  });

  return {
    checkShouldEditAdditionalServices,
  };
};
