import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { useActionAtom } from '@moego/client-lib-jotai/action';
import {
  fetchGroomingServiceAvailabilityAction,
  type FetchGroomingServiceAvailabilityResponse,
} from 'state/boardingDaycare/appointmentAtom';
import { checkGroomingAvailability } from 'state/boardingDaycare/serviceAtom.utils';
import { getAllDatesBetween } from 'utils/date';
import { useAdditionalGroomingStuffs } from './useAdditionalGroomingStuffs';
import { useGetScheduleDate } from './useGetScheduleDate';

export const useCheckGroomingServiceAvailability = () => {
  const { getScheduleDate } = useGetScheduleDate();
  const { additionalGroomingPetAndServiceDetailList, additionalGroomingPetAndServiceDetailMap } =
    useAdditionalGroomingStuffs();
  const fetchGroomingServiceAvailability = useActionAtom(fetchGroomingServiceAvailabilityAction);

  const checkAvailability = useLatestCallback(async () => {
    const { startDate, endDate } = getScheduleDate();
    const dates = getAllDatesBetween(startDate, endDate);
    const requestInputList = additionalGroomingPetAndServiceDetailList.flatMap((item) => item.detailList);

    const requestOutputList = await Promise.all(
      requestInputList.map(async (petDetail) => {
        const { petId, serviceId, petInfo } = petDetail;
        const serviceIdNumberVer = Number(serviceId);
        const petIdNumberVer = Number(petId);
        const params = {
          petParamList: [
            {
              breed: petInfo.breed,
              petId: petIdNumberVer,
              petTypeId: petInfo.petType,
            },
          ],
          petServices: {
            [petId]: [serviceIdNumberVer],
          },
          serviceIds: [serviceIdNumberVer],
          dates,
        };

        const res = await fetchGroomingServiceAvailability(params);
        return res;
      }),
    );

    const serviceDetailKeyMapToResponse = requestOutputList.reduce((acc, cur, index) => {
      const { serviceDetailKey } = requestInputList[index];
      acc[serviceDetailKey] = cur;
      return acc;
    }, {} as Record<string, FetchGroomingServiceAvailabilityResponse>);

    const getPetServiceAvailableDates = (serviceDetailKey: string) => {
      const datesAvailability = serviceDetailKeyMapToResponse?.[serviceDetailKey];
      return checkGroomingAvailability(datesAvailability, dates);
    };

    const getPetServiceHasAvailability = (serviceDetailKey: string) => {
      const availableDates = getPetServiceAvailableDates(serviceDetailKey);
      return !!availableDates.length;
    };

    const petIdMapToUnavailableServiceDetailKeyList = requestOutputList.reduce((acc, _cur, index) => {
      const { serviceDetailKey, petId } = requestInputList[index];
      const hasAvailability = getPetServiceHasAvailability(serviceDetailKey);
      if (!hasAvailability) {
        acc[petId] = acc[petId] || [];
        acc[petId].push(serviceDetailKey);
      }
      return acc;
    }, {} as Record<string, string[]>);

    const hasUnavailableGroomingServices = Object.keys(petIdMapToUnavailableServiceDetailKeyList).length > 0;

    return {
      requestOutputList,
      serviceDetailKeyMapToResponse,
      getPetServiceAvailableDates,
      getPetServiceHasAvailability,
      petIdMapToUnavailableServiceDetailKeyList,
      additionalGroomingPetAndServiceDetailMap,
      hasUnavailableGroomingServices,
    };
  });

  return {
    checkAvailability,
  };
};
