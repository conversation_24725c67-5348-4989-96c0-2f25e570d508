import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { atom, useAtom } from 'jotai';
import { createContext, useContext, type PropsWithChildren } from 'react';
import { type UnavailableServiceInfo } from '../../../../components/PartialNoAvailabilityModal';

// 综合状态管理 atom，包含所有相关的共享状态
export interface AppointmentEditSharedState {
  // 原有的 grooming availability 数据
  groomingAvailabilityData: {
    getPetServiceAvailableDates: (serviceDetailKey: string) => string[];
  } | null;
  // 被移除的服务详情键列表
  removedServiceDetailKeys: string[];
  // 不可用的服务信息列表
  unavailableServices: UnavailableServiceInfo[];
}

const defaultSharedState: AppointmentEditSharedState = {
  groomingAvailabilityData: null,
  removedServiceDetailKeys: [],
  unavailableServices: [],
};

// 主要的共享状态 atom
export const appointmentEditSharedStateAtom = atom<AppointmentEditSharedState>(defaultSharedState);

// 重置 atom，用于页面重新进入时清理状态
export const resetAppointmentEditSharedStateAtom = atom(null, (_get, set) => {
  set(appointmentEditSharedStateAtom, defaultSharedState);
});

// 为了向后兼容，导出原来的 groomingAvailabilityDataAtom
// 这个 atom 现在从 appointmentEditSharedStateAtom 中派生
export const groomingAvailabilityDataAtom = atom(
  (get) => get(appointmentEditSharedStateAtom).groomingAvailabilityData,
  (get, set, data: { getPetServiceAvailableDates: (serviceDetailKey: string) => string[] } | null) => {
    const currentState = get(appointmentEditSharedStateAtom);
    set(appointmentEditSharedStateAtom, {
      ...currentState,
      groomingAvailabilityData: data,
    });
  }
);

interface GroomingAvailabilityContextValue {
  // 原有的方法
  getAvailableDates: (serviceDetailKey: string) => string[];
  setAvailabilityData: (data: { getPetServiceAvailableDates: (serviceDetailKey: string) => string[] }) => void;

  // 新增的状态管理方法
  getRemovedServiceDetailKeys: () => string[];
  setRemovedServiceDetailKeys: (keys: string[]) => void;
  addRemovedServiceDetailKey: (key: string) => void;
  removeRemovedServiceDetailKey: (key: string) => void;

  getUnavailableServices: () => UnavailableServiceInfo[];
  setUnavailableServices: (services: UnavailableServiceInfo[]) => void;

  // 重置所有状态
  resetSharedState: () => void;
}

const GroomingAvailabilityContext = createContext<GroomingAvailabilityContextValue>({
  getAvailableDates: () => [],
  setAvailabilityData: () => {},
  getRemovedServiceDetailKeys: () => [],
  setRemovedServiceDetailKeys: () => {},
  addRemovedServiceDetailKey: () => {},
  removeRemovedServiceDetailKey: () => {},
  getUnavailableServices: () => [],
  setUnavailableServices: () => {},
  resetSharedState: () => {},
});

export const useGroomingAvailabilityContext = () => useContext(GroomingAvailabilityContext);

export const GroomingAvailabilityProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [sharedState, setSharedState] = useAtom(appointmentEditSharedStateAtom);

  // 原有的 grooming availability 相关方法
  const getAvailableDates = useLatestCallback((serviceDetailKey: string) => {
    if (!sharedState.groomingAvailabilityData) return [];
    return sharedState.groomingAvailabilityData.getPetServiceAvailableDates(serviceDetailKey) || [];
  });

  const setAvailabilityData = useLatestCallback((data: { getPetServiceAvailableDates: (serviceDetailKey: string) => string[] }) => {
    setSharedState(prev => ({
      ...prev,
      groomingAvailabilityData: data,
    }));
  });

  // removedServiceDetailKeys 相关方法
  const getRemovedServiceDetailKeys = useLatestCallback(() => {
    return sharedState.removedServiceDetailKeys;
  });

  const setRemovedServiceDetailKeys = useLatestCallback((keys: string[]) => {
    setSharedState(prev => ({
      ...prev,
      removedServiceDetailKeys: keys,
    }));
  });

  const addRemovedServiceDetailKey = useLatestCallback((key: string) => {
    setSharedState(prev => ({
      ...prev,
      removedServiceDetailKeys: [...prev.removedServiceDetailKeys.filter(k => k !== key), key],
    }));
  });

  const removeRemovedServiceDetailKey = useLatestCallback((key: string) => {
    setSharedState(prev => ({
      ...prev,
      removedServiceDetailKeys: prev.removedServiceDetailKeys.filter(k => k !== key),
    }));
  });

  // unavailableServices 相关方法
  const getUnavailableServices = useLatestCallback(() => {
    return sharedState.unavailableServices;
  });

  const setUnavailableServices = useLatestCallback((services: UnavailableServiceInfo[]) => {
    setSharedState(prev => ({
      ...prev,
      unavailableServices: services,
    }));
  });

  // 重置状态方法
  const resetSharedState = useLatestCallback(() => {
    setSharedState(defaultSharedState);
  });

  const contextValue = {
    getAvailableDates,
    setAvailabilityData,
    getRemovedServiceDetailKeys,
    setRemovedServiceDetailKeys,
    addRemovedServiceDetailKey,
    removeRemovedServiceDetailKey,
    getUnavailableServices,
    setUnavailableServices,
    resetSharedState,
  };

  return <GroomingAvailabilityContext.Provider value={contextValue}>{children}</GroomingAvailabilityContext.Provider>;
};
