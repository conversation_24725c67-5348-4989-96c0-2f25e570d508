import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { atom, useAtom } from 'jotai';
import { createContext, useContext, type PropsWithChildren } from 'react';

// Atom to store grooming availability data
export const groomingAvailabilityDataAtom = atom<{
  getPetServiceAvailableDates: (serviceDetailKey: string) => string[];
} | null>(null);

interface GroomingAvailabilityContextValue {
  getAvailableDates: (serviceDetailKey: string) => string[];
  setAvailabilityData: (data: { getPetServiceAvailableDates: (serviceDetailKey: string) => string[] }) => void;
}

const GroomingAvailabilityContext = createContext<GroomingAvailabilityContextValue>({
  getAvailableDates: () => [],
  setAvailabilityData: () => {},
});

export const useGroomingAvailabilityContext = () => useContext(GroomingAvailabilityContext);

export const GroomingAvailabilityProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [availabilityData, setAvailabilityData] = useAtom(groomingAvailabilityDataAtom);

  const getAvailableDates = useLatestCallback((serviceDetailKey: string) => {
    if (!availabilityData) return [];
    return availabilityData.getPetServiceAvailableDates(serviceDetailKey) || [];
  });

  const contextValue = {
    getAvailableDates,
    setAvailabilityData,
  };

  return <GroomingAvailabilityContext.Provider value={contextValue}>{children}</GroomingAvailabilityContext.Provider>;
};
