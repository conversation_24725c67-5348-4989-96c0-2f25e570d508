import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { useActionAtom } from '@moego/client-lib-jotai/action';
import { useSetAtom } from 'jotai';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import toast from 'react-hot-toast';
import {
  refreshBDAppointmentDetailAction,
  updateAppointmentAction,
  updateBookingRequestAction,
} from 'state/boardingDaycare/appointmentAtom';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { ApptDetailModal, useAppointmentDetailEditModals } from '../AppointmentDetailEditModals.context';
import { useCheckAvailability } from './useCheckAvailability';
import { useCheckGroomingServiceAvailability } from './useCheckGroomingServiceAvailability';
import { useCheckShouldEditAdditionalServices } from './useCheckShouldEditAdditionalServices';
import { useGetApptRescheduleSubmitParams } from './useGetApptRescheduleSubmitParams';
import { groomingAvailabilityDataAtom } from './useGroomingAvailabilityContext';

export const useRescheduleBoardingAppt = () => {
  const { openModal, closeModal } = useAppointmentDetailEditModals();
  const { initApptDetailEditState } = useAppointmentDetailEdit();
  const { getApptRescheduleSubmitParams } = useGetApptRescheduleSubmitParams();
  const refreshAppointmentDetail = useActionAtom(refreshBDAppointmentDetailAction);
  const { checkAvailability } = useCheckAvailability();
  const { checkAvailability: checkGroomingAvailability } = useCheckGroomingServiceAvailability();
  const { checkShouldEditAdditionalServices } = useCheckShouldEditAdditionalServices();
  const updateAppointment = useActionAtom(updateAppointmentAction);
  const updateBookingRequest = useActionAtom(updateBookingRequestAction);
  const appointmentDetailData = useAppointmentDetail();
  const setGroomingAvailabilityData = useSetAtom(groomingAvailabilityDataAtom);

  const handleEditScheduleDate = useLatestCallback(async () => {
    openModal(ApptDetailModal.EditScheduleDate, {
      onNext: async () => {
        const isDateTimeAvailable = await checkAvailability();
        if (!isDateTimeAvailable) {
          closeModal(ApptDetailModal.EditScheduleDate, true);
          openModal(ApptDetailModal.NoAvailability, {
            onClose: () => {
              closeModal(ApptDetailModal.NoAvailability, true);
              // 关闭之后重来
              handleEditScheduleDate();
            },
          });
          return;
        }

        // Check grooming service availability
        const groomingAvailabilityResult = await checkGroomingAvailability();
        const { hasUnavailableGroomingServices, petIdMapToUnavailableServiceDetailKeyList } =
          groomingAvailabilityResult;

        // Store the grooming availability data for later use in additional service dates modal
        setGroomingAvailabilityData(groomingAvailabilityResult);

        if (hasUnavailableGroomingServices) {
          // Convert to unavailable services info for the modal
          const unavailableServices = Object.entries(petIdMapToUnavailableServiceDetailKeyList).flatMap(
            ([petId, serviceDetailKeys]) => {
              return serviceDetailKeys.map((serviceDetailKey) => {
                // Get pet and service info from additionalGroomingPetAndServiceDetailMap
                const serviceDetail =
                  groomingAvailabilityResult.additionalGroomingPetAndServiceDetailMap?.[serviceDetailKey];
                return {
                  petId,
                  petName: serviceDetail?.petInfo?.petName || 'Unknown Pet',
                  serviceName: serviceDetail?.serviceName || 'Unknown Service',
                  serviceDetailKey,
                };
              });
            },
          );

          closeModal(ApptDetailModal.EditScheduleDate, true);
          openModal(ApptDetailModal.PartialNoAvailability, {
            unavailableServices,
            onPrev: () => {
              closeModal(ApptDetailModal.PartialNoAvailability, true);
              handleEditScheduleDate();
            },
            onNext: async () => {
              closeModal(ApptDetailModal.PartialNoAvailability, true);
              // Store the removed service detail keys for later use in submit
              const removedServiceDetailKeys = Object.values(petIdMapToUnavailableServiceDetailKeyList).flat();
              // Continue with the flow, the unavailable services will be handled in submit
              await handleEditTime(removedServiceDetailKeys);
            },
          });
          return;
        }

        closeModal(ApptDetailModal.EditScheduleDate, true);
        await handleEditTime();
      },
      onClose: () => {
        closeModal(ApptDetailModal.EditScheduleDate);
      },
    });
  });

  const handleEditTime = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    openModal(ApptDetailModal.EditTimes, {
      onClose: () => {
        closeModal(ApptDetailModal.EditTimes);
      },
      onNext: async () => {
        closeModal(ApptDetailModal.EditTimes, true);
        const shouldEditAdditionalServices = await checkShouldEditAdditionalServices(removedServiceDetailKeys);
        if (shouldEditAdditionalServices) {
          handleEditAdditionalServices(removedServiceDetailKeys);
          return;
        }
        handlePreviewChanges(() => handleSubmitBooking(removedServiceDetailKeys), removedServiceDetailKeys);
      },
      onPrev: async () => {
        closeModal(ApptDetailModal.EditTimes, true);
        await handleEditScheduleDate();
      },
    });
  });

  const handlePreviewChanges = useLatestCallback(
    async (onSubmit?: () => Promise<void>, removedServiceDetailKeys?: string[]) => {
      openModal(ApptDetailModal.PreviewChangesModal, {
        removedServiceDetailKeys,
        onClose: () => {
          closeModal(ApptDetailModal.PreviewChangesModal);
        },
        onSubmit: async () => {
          await onSubmit?.();
          closeModal(ApptDetailModal.PreviewChangesModal);
        },
        onPrev: async () => {
          closeModal(ApptDetailModal.PreviewChangesModal, true);
          const shouldEditAdditionalServices = await checkShouldEditAdditionalServices(removedServiceDetailKeys);
          if (shouldEditAdditionalServices) {
            handleEditAdditionalServices(removedServiceDetailKeys);
          } else {
            handleEditTime(removedServiceDetailKeys);
          }
        },
      });
    },
  );

  const handleEditAdditionalServices = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    openModal(ApptDetailModal.AdditionalServiceDatesEditModal, {
      removedServiceDetailKeys,
      onClose: () => {
        closeModal(ApptDetailModal.AdditionalServiceDatesEditModal);
      },
      onNext: () => {
        closeModal(ApptDetailModal.AdditionalServiceDatesEditModal, true);
        handlePreviewChanges(() => handleSubmitBooking(removedServiceDetailKeys), removedServiceDetailKeys);
      },
      onPrev: () => {
        closeModal(ApptDetailModal.AdditionalServiceDatesEditModal, true);
        handleEditTime(removedServiceDetailKeys);
      },
    });
  });

  const handleSubmitBooking = useLatestCallback(async (removedServiceDetailKeys?: string[]) => {
    const petAndServices = await getApptRescheduleSubmitParams();
    const isBookingRequest = appointmentDetailData?.appointment?.isBookingRequest;

    if (isBookingRequest) {
      await updateBookingRequest({
        petAndServices,
        removedServiceDetailKeys,
      });
    } else {
      await updateAppointment({
        petAndServices,
        removedServiceDetailKeys,
      });
    }

    await refreshAppointmentDetail();
    toast.success('Successfully updated.');
  });

  const handleRescheduleBoardingAppt = useLatestCallback(async () => {
    initApptDetailEditState();
    handleEditScheduleDate();
  });

  return {
    handleRescheduleBoardingAppt,
  };
};
