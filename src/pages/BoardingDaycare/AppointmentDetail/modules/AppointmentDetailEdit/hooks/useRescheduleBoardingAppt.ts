import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { useActionAtom } from '@moego/client-lib-jotai/action';
import { useAppointmentDetail } from 'pages/BoardingDaycare/AppointmentDetail/hooks/useAppointmentDetail';
import toast from 'react-hot-toast';
import {
  refreshBDAppointmentDetailAction,
  updateAppointmentAction,
  updateBookingRequestAction,
} from 'state/boardingDaycare/appointmentAtom';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { ApptDetailModal, useAppointmentDetailEditModals } from '../AppointmentDetailEditModals.context';
import { useCheckAvailability } from './useCheckAvailability';
import { useCheckGroomingServiceAvailability } from './useCheckGroomingServiceAvailability';
import { useCheckShouldEditAdditionalServices } from './useCheckShouldEditAdditionalServices';
import { useGetApptRescheduleSubmitParams } from './useGetApptRescheduleSubmitParams';
import { useGroomingAvailabilityContext } from './useGroomingAvailabilityContext';

export const useRescheduleBoardingAppt = () => {
  const { openModal, closeModal } = useAppointmentDetailEditModals();
  const { initApptDetailEditState } = useAppointmentDetailEdit();
  const { getApptRescheduleSubmitParams } = useGetApptRescheduleSubmitParams();
  const refreshAppointmentDetail = useActionAtom(refreshBDAppointmentDetailAction);
  const { checkAvailability } = useCheckAvailability();
  const { checkAvailability: checkGroomingAvailability } = useCheckGroomingServiceAvailability();
  const { checkShouldEditAdditionalServices } = useCheckShouldEditAdditionalServices();
  const updateAppointment = useActionAtom(updateAppointmentAction);
  const updateBookingRequest = useActionAtom(updateBookingRequestAction);
  const appointmentDetailData = useAppointmentDetail();

  // 使用新的 context 来管理共享状态
  const {
    setAvailabilityData,
    getRemovedServiceDetailKeys,
    setRemovedServiceDetailKeys,
    setUnavailableServices,
  } = useGroomingAvailabilityContext();

  const handleEditScheduleDate = useLatestCallback(async () => {
    openModal(ApptDetailModal.EditScheduleDate, {
      onNext: async () => {
        const isDateTimeAvailable = await checkAvailability();
        if (!isDateTimeAvailable) {
          closeModal(ApptDetailModal.EditScheduleDate, true);
          openModal(ApptDetailModal.NoAvailability, {
            onClose: () => {
              closeModal(ApptDetailModal.NoAvailability, true);
              // 关闭之后重来
              handleEditScheduleDate();
            },
          });
          return;
        }

        // Check grooming service availability
        const groomingAvailabilityResult = await checkGroomingAvailability();
        const { hasUnavailableGroomingServices, petIdMapToUnavailableServiceDetailKeyList } =
          groomingAvailabilityResult;

        // Store the grooming availability data for later use in additional service dates modal
        setAvailabilityData(groomingAvailabilityResult);

        if (hasUnavailableGroomingServices) {
          // Convert to unavailable services info for the modal
          const unavailableServices = Object.entries(petIdMapToUnavailableServiceDetailKeyList).flatMap(
            ([petId, serviceDetailKeys]) => {
              return serviceDetailKeys.map((serviceDetailKey) => {
                // Get pet and service info from additionalGroomingPetAndServiceDetailMap
                const serviceDetail =
                  groomingAvailabilityResult.additionalGroomingPetAndServiceDetailMap?.[serviceDetailKey];
                return {
                  petId,
                  petName: serviceDetail?.petInfo?.petName || 'Unknown Pet',
                  serviceName: serviceDetail?.serviceName || 'Unknown Service',
                  serviceDetailKey,
                };
              });
            },
          );

          // Store the unavailable services and removed service detail keys in context
          setUnavailableServices(unavailableServices);
          const removedServiceDetailKeys = Object.values(petIdMapToUnavailableServiceDetailKeyList).flat();
          setRemovedServiceDetailKeys(removedServiceDetailKeys);

          closeModal(ApptDetailModal.EditScheduleDate, true);
          openModal(ApptDetailModal.PartialNoAvailability, {
            onPrev: () => {
              closeModal(ApptDetailModal.PartialNoAvailability, true);
              handleEditScheduleDate();
            },
            onNext: async () => {
              closeModal(ApptDetailModal.PartialNoAvailability, true);
              // Continue with the flow, the unavailable services will be handled in submit
              await handleEditTime();
            },
          });
          return;
        }

        closeModal(ApptDetailModal.EditScheduleDate, true);
        await handleEditTime();
      },
      onClose: () => {
        closeModal(ApptDetailModal.EditScheduleDate);
      },
    });
  });

  const handleEditTime = useLatestCallback(async () => {
    openModal(ApptDetailModal.EditTimes, {
      onClose: () => {
        closeModal(ApptDetailModal.EditTimes);
      },
      onNext: async () => {
        closeModal(ApptDetailModal.EditTimes, true);
        const shouldEditAdditionalServices = await checkShouldEditAdditionalServices();
        if (shouldEditAdditionalServices) {
          handleEditAdditionalServices();
          return;
        }
        handlePreviewChanges(() => handleSubmitBooking());
      },
      onPrev: async () => {
        closeModal(ApptDetailModal.EditTimes, true);
        await handleEditScheduleDate();
      },
    });
  });

  const handlePreviewChanges = useLatestCallback(
    async (onSubmit?: () => Promise<void>) => {
      openModal(ApptDetailModal.PreviewChangesModal, {
        onClose: () => {
          closeModal(ApptDetailModal.PreviewChangesModal);
        },
        onSubmit: async () => {
          await onSubmit?.();
          closeModal(ApptDetailModal.PreviewChangesModal);
        },
        onPrev: async () => {
          closeModal(ApptDetailModal.PreviewChangesModal, true);
          const shouldEditAdditionalServices = await checkShouldEditAdditionalServices();
          if (shouldEditAdditionalServices) {
            handleEditAdditionalServices();
          } else {
            handleEditTime();
          }
        },
      });
    },
  );

  const handleEditAdditionalServices = useLatestCallback(async () => {
    openModal(ApptDetailModal.AdditionalServiceDatesEditModal, {
      onClose: () => {
        closeModal(ApptDetailModal.AdditionalServiceDatesEditModal);
      },
      onNext: () => {
        closeModal(ApptDetailModal.AdditionalServiceDatesEditModal, true);
        handlePreviewChanges(() => handleSubmitBooking());
      },
      onPrev: () => {
        closeModal(ApptDetailModal.AdditionalServiceDatesEditModal, true);
        handleEditTime();
      },
    });
  });

  const handleSubmitBooking = useLatestCallback(async () => {
    const petAndServices = await getApptRescheduleSubmitParams();
    const isBookingRequest = appointmentDetailData?.appointment?.isBookingRequest;
    const removedServiceDetailKeys = getRemovedServiceDetailKeys();

    if (isBookingRequest) {
      await updateBookingRequest({
        petAndServices,
        removedServiceDetailKeys,
      });
    } else {
      await updateAppointment({
        petAndServices,
        removedServiceDetailKeys,
      });
    }

    await refreshAppointmentDetail();
    toast.success('Successfully updated.');
  });

  const handleRescheduleBoardingAppt = useLatestCallback(async () => {
    initApptDetailEditState();
    handleEditScheduleDate();
  });

  return {
    handleRescheduleBoardingAppt,
  };
};
