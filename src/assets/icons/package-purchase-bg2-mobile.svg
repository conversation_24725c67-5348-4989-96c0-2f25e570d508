<svg width="335" height="98" viewBox="0 0 335 98" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path
    d="M426.118 140.363C396.002 88.3042 290.62 -40.2802 136.407 159.963C-17.8049 360.207 -137.649 204.704 -170.605 180.527"
    stroke="url(#paint0_linear_200_5327)" stroke-opacity="0.1" stroke-width="101.426"
    stroke-linecap="round" />
  <defs>
    <linearGradient id="paint0_linear_200_5327" x1="128" y1="135" x2="206.494" y2="211.05"
      gradientUnits="userSpaceOnUse">
      <!-- 需要注意，由于涉及主题色，这里是一个变量，不能直接换 figma 导出切图 -->
      <stop offset="0.187814" stop-color="rgb(var(--color-primary))" />
      <stop offset="1" stop-color="#FFF7F0" />
    </linearGradient>
  </defs>
</svg>
