<svg xmlns="http://www.w3.org/2000/svg" width="259" height="55" viewBox="0 0 259 55" fill="none">
  <path d="M376.823 141.637C348.082 95.0103 242.485 -24.0341 51.5151 121.536"
    stroke="url(#paint0_linear_200_5260)" stroke-opacity="0.1" stroke-width="101.426"
    stroke-linecap="round" />
  <defs>
    <linearGradient id="paint0_linear_200_5260" x1="349.03" y1="38.9273" x2="-77.75" y2="46.5278"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.13975" stop-color="white" />
      <!-- 需要注意，由于涉及主题色，这里是一个变量，不能直接换 figma 导出切图 -->
      <stop offset="0.611603" stop-color="rgb(var(--color-primary))" />
    </linearGradient>
  </defs>
</svg>
