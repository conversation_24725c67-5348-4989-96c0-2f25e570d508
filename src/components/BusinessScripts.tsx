import { useAtomValue } from 'jotai';
import { useEffect, useRef } from 'react';
import { businessScriptsState } from 'state/business/state';
import { loadScript } from 'utils/loadScript';
import { validateScriptsList } from 'utils/scriptSecurity';

interface ScriptConfig {
  type: 'script';
  src?: string;
  content?: string;
  async?: boolean;
  defer?: boolean;
  crossOrigin?: string;
}

interface StyleConfig {
  type: 'style';
  href?: string;
  content?: string;
  media?: string;
  crossOrigin?: string;
}

type ResourceConfig = ScriptConfig | StyleConfig;

// 全局记录已加载的资源，防止重复加载
const loadedResources = new Set<string>();

/**
 * 使用DOMParser解析包含多个script和style标签的字符串
 */
function parseResourceString(resourceString: string): ResourceConfig[] {
  const resources: ResourceConfig[] = [];

  try {
    // 使用DOMParser解析HTML字符串
    const parser = new DOMParser();
    const doc = parser.parseFromString(resourceString, 'text/html');

    // 解析script标签
    const scriptElements = doc.querySelectorAll('script');
    scriptElements.forEach((script) => {
      const src = script.getAttribute('src');
      const content = script.textContent?.trim();
      const async = script.hasAttribute('async');
      const defer = script.hasAttribute('defer');
      const crossOrigin = script.getAttribute('crossorigin');

      if (src) {
        // 外部JavaScript
        resources.push({
          type: 'script',
          src,
          async,
          defer,
          crossOrigin: crossOrigin || undefined,
        });
      } else if (content) {
        // 内联JavaScript
        resources.push({
          type: 'script',
          content,
          async,
          defer,
          crossOrigin: crossOrigin || undefined,
        });
      }
    });

    // 解析link标签（CSS）
    const linkElements = doc.querySelectorAll('link[rel="stylesheet"]');
    linkElements.forEach((link) => {
      const href = link.getAttribute('href');
      const media = link.getAttribute('media');
      const crossOrigin = link.getAttribute('crossorigin');

      if (href) {
        resources.push({
          type: 'style',
          href,
          media: media || undefined,
          crossOrigin: crossOrigin || undefined,
        });
      }
    });

    // 解析style标签
    const styleElements = doc.querySelectorAll('style');
    styleElements.forEach((style) => {
      const content = style.textContent?.trim();
      const media = style.getAttribute('media');

      if (content) {
        resources.push({
          type: 'style',
          content,
          media: media || undefined,
        });
      }
    });
  } catch (error) {
    console.error('Error parsing resource string with DOMParser:', error);
  }

  return resources;
}

/**
 * 生成资源的唯一标识
 */
function getResourceId(resource: ResourceConfig): string {
  if (resource.type === 'script') {
    if (resource.src) {
      return `script:src:${resource.src}`;
    }
    if (resource.content) {
      const hash = resource.content
        .split('')
        .reduce((a, b) => {
          a = (a << 5) - a + b.charCodeAt(0);
          return a & a;
        }, 0)
        .toString(36);
      return `script:content:${hash}`;
    }
  } else if (resource.type === 'style') {
    if (resource.href) {
      return `style:href:${resource.href}`;
    }
    if (resource.content) {
      const hash = resource.content
        .split('')
        .reduce((a, b) => {
          a = (a << 5) - a + b.charCodeAt(0);
          return a & a;
        }, 0)
        .toString(36);
      return `style:content:${hash}`;
    }
  }
  return `unknown:${Date.now()}`;
}

/**
 * 安全地加载资源到header中
 */
function loadResourceToHeader(resource: ResourceConfig): boolean {
  try {
    const resourceId = getResourceId(resource);

    // 检查是否已经加载过
    if (loadedResources.has(resourceId)) {
      return false;
    }

    if (resource.type === 'script') {
      // 处理脚本
      if (resource.src) {
        // 外部JavaScript
        const script = loadScript(resource.src, {
          async: resource.async,
          crossOrigin: resource.crossOrigin,
        });

        script.onerror = (error) => {
          console.error('Failed to load external script:', resource.src, error);
          loadedResources.delete(resourceId);
        };
      } else if (resource.content) {
        // 内联JavaScript
        const script = document.createElement('script');
        script.textContent = resource.content;
        script.async = resource.async || false;
        script.defer = resource.defer || false;
        if (resource.crossOrigin) {
          script.crossOrigin = resource.crossOrigin;
        }

        script.onerror = (error) => {
          console.error('Failed to load inline script:', error);
          loadedResources.delete(resourceId);
        };

        document.head.appendChild(script);
      }
    } else if (resource.type === 'style') {
      // 处理样式
      if (resource.href) {
        // 外部CSS
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = resource.href;
        if (resource.media) {
          link.media = resource.media;
        }
        if (resource.crossOrigin) {
          link.crossOrigin = resource.crossOrigin;
        }

        link.onerror = (error) => {
          console.error('Failed to load external CSS:', resource.href, error);
          loadedResources.delete(resourceId);
        };

        document.head.appendChild(link);
      } else if (resource.content) {
        // 内联CSS
        const style = document.createElement('style');
        style.textContent = resource.content;
        if (resource.media) {
          style.media = resource.media;
        }

        document.head.appendChild(style);
      }
    }

    // 标记为已加载
    loadedResources.add(resourceId);
    return true;
  } catch (err) {
    console.error('Error loading resource:', resource, err);
    return false;
  }
}

export function BusinessScripts() {
  const scriptsLoadable = useAtomValue(businessScriptsState);
  const hasLoadedRef = useRef(false);

  useEffect(() => {
    if (hasLoadedRef.current) {
      return;
    }

    if (!scriptsLoadable || !scriptsLoadable.data) {
      return;
    }

    const resourceConfigs: ResourceConfig[] = parseResourceString(scriptsLoadable.data);

    if (resourceConfigs.length === 0) {
      return;
    }

    // 使用安全验证函数验证脚本配置（只验证脚本部分）
    const scriptConfigs = resourceConfigs.filter((r): r is ScriptConfig => r.type === 'script');
    const validScripts = validateScriptsList(scriptConfigs);

    // 过滤出有效的脚本和所有样式
    const validResources = resourceConfigs.filter((resource) => {
      if (resource.type === 'script') {
        // 这是脚本，需要验证
        return validScripts.includes(resource);
      }
      // 这是样式，暂时都允许
      return true;
    });

    if (validResources.length === 0) {
      return;
    }

    setTimeout(() => {
      for (const resourceConfig of validResources) {
        loadResourceToHeader(resourceConfig);
      }

      hasLoadedRef.current = true;
    }, 0);
  }, [scriptsLoadable]);

  return null;
}
