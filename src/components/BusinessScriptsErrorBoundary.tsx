import React from 'react';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Business Scripts 错误边界
 * 防止脚本加载错误影响整个应用
 */
export class BusinessScriptsErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新状态，下次渲染时显示错误UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误信息
    console.error('Business Scripts Error Boundary caught an error:', error, errorInfo);

    // 可以在这里发送错误报告到监控服务
    // captureException(error, { contexts: { businessScripts: errorInfo } });
  }

  render() {
    if (this.state.hasError) {
      // 错误时返回null，不影响其他组件
      console.warn('Business Scripts failed to load, continuing without custom scripts');
      return null;
    }

    return this.props.children;
  }
}
