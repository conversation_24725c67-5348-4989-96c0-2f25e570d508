import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { bdMetaQuestionsState, bookingCustomerQuestionsState, QuestionType } from 'state/question/state';
import {
  type CustomerQuestionKey,
  type MetaFeedingKeyEnum,
  type MetaMedicationKeyEnum,
  type MetaQuestionKey,
} from 'types/question';
import { safeJsonParse } from 'utils/parse';

export const useClientQuestionConfig = (key: CustomerQuestionKey) => {
  const questions = useAtomValue(bookingCustomerQuestionsState).data;

  return useMemo(() => {
    const { isShow, isRequired } = questions?.find((question) => question.key === key) || {};
    return {
      isShow: Bo<PERSON>an(isShow),
      isRequired: <PERSON><PERSON><PERSON>(isRequired && isShow),
    };
  }, [questions, key]);
};

interface MetaQuestionChildren {
  isShow: boolean;
  isRequired: boolean;
  question: string;
  type: MetaFeedingKeyEnum | MetaMedicationKeyEnum;
}
export const useBDMetaQuestionConfig = (key: MetaQuestionKey, type: QuestionType) => {
  const { data: bdMetaQuestions } = useAtomValue(bdMetaQuestionsState);

  return useMemo(() => {
    const questions = type === QuestionType.Boarding ? bdMetaQuestions?.boarding : bdMetaQuestions?.daycare;
    const { isShow, isRequired, extraJson } = questions?.find((question) => question.key === key) || {};
    const children: MetaQuestionChildren[] = safeJsonParse<MetaQuestionChildren[]>(extraJson, []).map((item) => ({
      ...item,
      // 向下兼容：由于是 extraJson 新增字段，我们默认为 true，避免在未刷数据的情况下，不该隐藏掉的选项被隐藏掉
      isShow: item.isShow ?? true,
    }));
    return {
      isShow: Boolean(isShow),
      isRequired: Boolean(isRequired && isShow),
      isPartialRequired: children.some((child) => child.isRequired),
      children,
      // 后台设置启用的 key
      activeChildKeys: children.filter((child) => child.isShow).map((child) => child.type),
      getByKey(key: MetaFeedingKeyEnum | MetaMedicationKeyEnum) {
        return children.find((child) => child.type === key);
      },
    };
  }, [bdMetaQuestions, key, type]);
};

export const getFormProps = (
  children: MetaQuestionChildren[],
  key: MetaFeedingKeyEnum | MetaMedicationKeyEnum,
  formKey: string,
) => {
  const child = children.find((child) => child.type === key);
  const name = FormKeyUtil.ownKey(formKey, key);
  if (!child) {
    return {
      name,
    };
  }
  return {
    name,
    label: child.question,
    rules: { required: child.isRequired },
  };
};

export const FormKeyUtil = {
  get joiner() {
    // 注意，. 在 react-hook-form 中会被解析为访问 object 的属性，所以这里不能用 . 作为 joiner
    return '-';
  },
  ownKey(...args: Array<number | string>) {
    if (this !== FormKeyUtil) {
      throw new Error('please use formKeyUtil as caller');
    }
    return args.join(this.joiner);
  },
  splitKey(key: string) {
    if (this !== FormKeyUtil) {
      throw new Error('please use formKeyUtil as caller');
    }
    return key.split(this.joiner);
  },
};
