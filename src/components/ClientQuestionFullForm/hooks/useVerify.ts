import { useAtomValue } from 'jotai';
import { type RegisterOptions } from 'react-hook-form';
import { checkCustomerExistsAction } from 'state/account/action';
import { customerInfoState } from 'state/account/state';
import { CustomerQuestionKey } from 'types/question';
import { validateEmail } from 'utils/validator';
import { useClientQuestionConfig } from './useQuestionConfig';

export interface UseVerifyOptions {
  skip?: boolean;
}

export const useVerifyEmail = (options?: UseVerifyOptions): RegisterOptions['validate'] => {
  const checkCustomerExists = useAtomValue(checkCustomerExistsAction);
  const { isRequired } = useClientQuestionConfig(CustomerQuestionKey.Email);
  const currentCustomerInfo = useAtomValue(customerInfoState);

  return async (email) => {
    try {
      if (options?.skip || (!email && !isRequired) /** 没填且不必填 */) {
        return true;
      }

      if (!email) {
        return 'This field is required';
      }

      const error = validateEmail(email);
      if (error) {
        return error;
      }

      // 如果为当前客户已登录的 email，则允许使用
      const currentCustomerEmail = currentCustomerInfo?.data?.email;
      if (currentCustomerEmail && email.toLowerCase() === currentCustomerEmail.toLowerCase()) {
        return true;
      }

      const exists = await checkCustomerExists({ email });
      if (exists) {
        return 'Email address already exists';
      }
    } catch (e) {
      const error = e as Error;
      return error.message;
    }
  };
};

export const useVerifyUserName = (options?: UseVerifyOptions): RegisterOptions['validate'] => {
  return async (userName) => {
    if (options?.skip) {
      return true;
    }

    const NAME_MAX_LENGTH = 50;
    const traverse = (v = '') => {
      if (!v.length) {
        return 'This field is required';
      }
      if (v.length > NAME_MAX_LENGTH) {
        return `Maximum ${NAME_MAX_LENGTH} characters`;
      }
    };

    return Object.values(userName)
      .map((item) => traverse(item as string))
      .find((v) => v);
  };
};
