import { ExistingClientAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { Form, useForm } from '@moego/client-lib-components/dist/Form/Form';
import { AddressSelect, type AddressInfoRef } from 'components/ClientQuestionFullForm/component/AddressSelect';
import { useClientQuestionConfig } from 'components/ClientQuestionFullForm/hooks/useQuestionConfig';
import { useVerifyEmail, useVerifyUserName } from 'components/ClientQuestionFullForm/hooks/useVerify';
import { MoeInput } from 'components/Form/components/MoeInput';
import { UserName } from 'components/Form/fields/UserName';
import {
  QuestionForm,
  type FormQuestionItem,
  type FormValues,
  type QuestionFormRef,
} from 'components/Form/QuestionForm';
import {
  useAdditionalQuestions,
  type AdditionalQuestionsConfig,
} from 'pages/PersonalInfo/hooks/useAdditionalQuestions';
import { forwardRef, memo, useEffect, useImperativeHandle, useRef, type ReactElement, type ReactNode } from 'react';
import { type Validate } from 'react-hook-form';
import { type CustomerInfoValue } from 'state/customer/state';
import { type BookingQuestionEntity } from 'types/entity';
import { CustomerQuestionKey } from 'types/question';
import { validatePhone } from 'utils/validator';
import { Condition } from 'widgets/Condition';

export interface ClientFullFormProps extends AdditionalQuestionsConfig {
  defaultValue: CustomerInfoValue;
  validateOnMount?: boolean;
  isOnlyBasic?: boolean;
  afterNameContent?: ReactNode;
  isNewClient?: boolean;
  hideDefaultPhoneNumber?: boolean;
  filterCustomizeQuestions?: boolean;
}

export interface ClientFullFormRef {
  submit: () => Promise<CustomerInfoValue>;
  form: ReturnType<typeof useForm<ClientBasicFormProps>>;
}

export interface ClientBasicFormProps {
  userName: { firstName: string; lastName: string };
  email?: string;
  address?: { address: string; isValid: boolean };
  phoneNumber?: string;
}

const isFieldViewOnly = (question: Partial<BookingQuestionEntity>, isExistingClient: boolean) => {
  return isExistingClient && question?.existingClientAccessMode === ExistingClientAccessMode.VIEW;
};

export const ClientFullForm = memo(
  forwardRef<ClientFullFormRef, ClientFullFormProps>((props, fullRef) => {
    const {
      defaultValue,
      validateOnMount,
      isOnlyBasic = false,
      afterNameContent,
      isNewClient,
      hideDefaultPhoneNumber,
      ...config
    } = props;
    const { basic, additional } = defaultValue;
    const emailConfig = useClientQuestionConfig(CustomerQuestionKey.Email);
    const verifyEmail = useVerifyEmail();
    const verifyUserName = useVerifyUserName();
    const addressRef = useRef<AddressInfoRef | null>(null);
    const additionalRef = useRef<QuestionFormRef | null>(null);
    const additionalQuestions = useAdditionalQuestions(config);
    const form = useForm<ClientBasicFormProps>();

    useImperativeHandle(
      fullRef,
      () => ({
        submit: async () => {
          const [basic, address, additional] = await Promise.all([
            await new Promise<ClientBasicFormProps>((res, rej) => {
              form.handleSubmit(res, rej)();
            }),
            await addressRef.current?.validate(),
            await new Promise<FormValues | undefined>(async (res, rej) => {
              if (!additionalRef.current) {
                res(undefined);
                return;
              }
              const v = await additionalRef.current.validate();
              // FIXME: undefined or null?
              v === undefined ? rej() : res(v as FormValues);
            }),
          ]);
          const { userName, email, phoneNumber } = basic;
          return { basic: { ...userName, email, address, phoneNumber }, additional };
        },
        form,
      }),
      [form],
    );

    useEffect(() => {
      const readonlyFields = additionalQuestions.filter((q) => isFieldViewOnly(q, true));
      additionalRef.current?.setReadOnlyFields(readonlyFields.map((q) => q.key));
    }, [additionalQuestions]);

    useEffect(() => {
      const { firstName, lastName, email, phoneNumber } = basic || {};

      // @ts-expect-error FIXME: 不知道改成 setValue({ firstName, lastName }) 可不可行
      form.setValue('userName', firstName && lastName ? { firstName, lastName } : undefined);
      form.setValue('email', email);
      form.setValue('phoneNumber', phoneNumber);
      basic?.address && addressRef.current?.setValue(basic?.address);
    }, [basic]);

    useEffect(() => {
      if (validateOnMount) {
        form.trigger();
      }
    }, [validateOnMount]);

    return (
      <Form form={form} className="gap-y-[20px]" shakeItemOnSubmitError>
        <Form.Item name="userName" rules={{ required: true, validate: verifyUserName }}>
          <UserName required />
        </Form.Item>
        <Condition if={!hideDefaultPhoneNumber}>
          <Form.Item name="phoneNumber" rules={{ validate: validatePhone as Validate<string, any> }}>
            <MoeInput
              label="Phone number"
              placeholder="Enter your phone number"
              pattern="\d*"
              maxLength={17}
              inputClassName="!bg-[#F3F3F3] !text-[#505050]"
              readOnly
            />
          </Form.Item>
        </Condition>
        {/* 内容插槽, 提供给verify question流程使用 */}
        {afterNameContent as ReactElement}
        <Condition if={emailConfig.isShow}>
          <Form.Item name="email" rules={{ required: emailConfig.isRequired, validate: verifyEmail }}>
            <MoeInput label="Email address" required={emailConfig.isRequired} placeholder="<EMAIL>" />
          </Form.Item>
        </Condition>

        <Condition if={!isOnlyBasic}>
          <AddressSelect ref={addressRef} defaultValue={basic?.address} />

          <QuestionForm
            formRef={additionalRef}
            className="space-y-[20px] px-0 child:!px-[0px] web:child:!px-[0px]"
            fields={
              additionalQuestions
                .filter((v) => v.id > 0)
                .map((item) => {
                  return {
                    ...item,
                    readOnly: isFieldViewOnly(item, !isNewClient),
                  };
                }) as FormQuestionItem[]
            }
            defaultValues={additional}
          />
        </Condition>
      </Form>
    );
  }),
);
