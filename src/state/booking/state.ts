import { type AddressModel } from '@moego/api-web/moego/models/map/v1/map_models';
import { atomWithCompatibleSessionStorage } from '@moego/client-lib-jotai/dist/atomWithCompatibleStorage';
import { AvailabilityType } from '@moego/client-lib-types/dist/business';
import { type CalendarDay } from '@moego/client-lib-widgets/dist/CalendarPicker/types';
import { default as dayjs } from 'dayjs';
import { atom } from 'jotai';
import { atomWithReset } from 'jotai/utils';
import { type SelectedPetServiceAddons } from 'pages/Service/ServiceAddonList/types';
import { type PetInfoInterface } from 'petFactory/petBasic';
import { petStoreAtom } from 'petFactory/petStore';
import { customerAddressesState, customerInfoState, didLoginState } from 'state/account/state';
import { atomWithBusinessCustomerScopedStorage } from 'state/account/utils';
import { businessInfoState } from 'state/business/state';
import {
  type AvailableTimesEntity,
  type BookingAvailableTimesEntity,
  type CustomerAddressEntity,
  type CustomerEntity,
  type StaffWorkingHourRangeEntity,
} from 'types/entity';
import { ID_ANONYMOUS } from 'types/id';
import { atomWithBusinessScopedStorage } from '../business/utils';
import { getBookingGroomersKey } from './utils';

export const bookingContactState = atomWithBusinessScopedStorage<
  Pick<CustomerEntity, 'firstName' | 'lastName' | 'email'>
>('bookingContact', {
  firstName: '',
  lastName: '',
  email: '',
});

export const bookingNewAddressState = atomWithBusinessCustomerScopedStorage<{
  address?: CustomerAddressEntity;
  addressModel?: AddressModel;
  outOfArea: boolean;
}>('bookingAddress', {
  outOfArea: false,
});

export const bookingAddressIndexState = atomWithReset(-1);

export const bookingAddressState = atom<
  | {
      isNew: boolean;
      address?: CustomerAddressEntity;
      outOfArea?: boolean;
    }
  | undefined
>((get) => {
  const addresses = get(customerAddressesState);
  const addressIndex = get(bookingAddressIndexState);
  const newAddress = get(bookingNewAddressState);
  if (newAddress.address) {
    return { isNew: true, address: newAddress.address, outOfArea: newAddress.outOfArea };
  }
  const address = addresses.data?.all?.[addressIndex];
  if (address) {
    const outOfArea = addresses.data?.outArea?.some((item) => item?.addressId === address?.addressId);
    return { isNew: false, address: address!, outOfArea };
  }
});

export const bookingServiceAndAddonState = atomWithReset<SelectedPetServiceAddons[]>([]);

export const bookingServiceIdAndAddonIdListState = atom((get) => {
  const bookingServiceAndAddon = get(bookingServiceAndAddonState);
  return bookingServiceAndAddon.map(({ petKey, service, addonList }) => {
    return {
      petKey,
      serviceId: service?.id,
      addonIdList: addonList.map((addon) => addon.id),
    };
  });
});

export const bookingGroomerState = atomWithReset<number | null>(ID_ANONYMOUS);

export const bookingGroomersState = atomWithReset<number[]>([]);

export enum BookingPreferredGroomerStateEnum {
  Anyone = 'anyone',
  Groomer = 'groomer',
}

export const bookingPreferredGroomerState = atomWithReset<BookingPreferredGroomerStateEnum | null>(null);

export const bookingAddressExtraInfoState = atom((get) => {
  const didLogin = get(didLoginState);
  const customerInfo = get(customerInfoState).data;
  const businessInfo = get(businessInfoState).data;
  if (!businessInfo || (didLogin && !customerInfo)) {
    return;
  }
  const { address } = get(bookingAddressState) || {};
  const startDate = dayjs().format('YYYY-MM-DD');

  return {
    lat: address?.lat,
    lng: address?.lng,
    zipcode: address?.zipcode,
    startDate,
  };
});

export const bookingTimeSlotRecordsState = atomWithReset<{ [key: string]: BookingAvailableTimesEntity }>({});

export const bookingTimeSlotsRecordsState = atomWithReset<{ [key: string]: AvailableTimesEntity }>({});

export const getMonthlyFirstAvailableAction = atom((get) => (monthStr: string): string => {
  const bookingTimeSlotRecords = get(bookingTimeSlotRecordsState) || {};
  const bookingGroomer = get(bookingGroomerState);
  const targetMonthKey = `${monthStr}_${bookingGroomer}`;
  return Object.keys(bookingTimeSlotRecords?.[targetMonthKey] ?? {})?.sort()?.[0];
});

export const getMonthlyFirstAvailableTimeSlotsAction = atom((get) => (monthStr: string): string => {
  const bookingTimeSlotsRecords = get(bookingTimeSlotsRecordsState) || {};
  const bookingGroomers = get(bookingGroomersState);
  const targetMonthKey = `${monthStr}${getBookingGroomersKey(bookingGroomers)}`;
  return Object.keys(bookingTimeSlotsRecords?.[targetMonthKey]?.availableDates ?? {})?.sort()?.[0];
});

export const getExactFirstAvailableAction = atom((get) => () => {
  const bookingTimeSlotRecords = get(bookingTimeSlotRecordsState) || {};
  const bookingGroomer = get(bookingGroomerState);
  const validMonthKeys =
    Object.keys(bookingTimeSlotRecords)
      ?.filter?.((key) => key.indexOf(`${bookingGroomer}`) !== -1)
      ?.sort() ?? [];
  const firstMonthKey = validMonthKeys.sort()?.[0];
  return Object.keys(bookingTimeSlotRecords?.[firstMonthKey] ?? {})?.sort()?.[0];
});

export const getExactFirstAvailableSlotDateAction = atom((get) => () => {
  const availableTimes = get(availableTimesState);
  const bookingTimeSlotsRecords = get(bookingTimeSlotsRecordsState);
  const bookingGroomers = get(bookingGroomersState);

  if (!bookingGroomers.length) {
    return Object.keys(availableTimes?.availableDates ?? {})?.sort()?.[0];
  }

  const validMonthKeys =
    Object.keys(bookingTimeSlotsRecords)
      ?.filter?.((key) => key.indexOf(`${getBookingGroomersKey(bookingGroomers)}`) !== -1)
      ?.sort() ?? [];
  const firstMonthKey = validMonthKeys.sort()?.[0];

  return Object.keys(bookingTimeSlotsRecords?.[firstMonthKey]?.availableDates ?? {})?.sort()?.[0];
});

export const bookingTimeSlotState = atomWithReset<{ day?: CalendarDay; time?: number; staff?: number; label?: string }>(
  {},
);

export const bookingTimeSlotsState = atomWithReset<{
  day?: CalendarDay;
  time?: number;
  petsStaffs?: { petId: number; staffId: number }[];
  label?: string;
}>({});

export const staffWorkingHourRecordsState = atomWithReset<{ [key: string]: StaffWorkingHourRangeEntity }>({});

export const landingAddressState = atom<{
  address?: CustomerAddressEntity;
  addressModel?: AddressModel;
  available?: boolean;
}>({});

export const fromLandingState = atom<boolean>(false);

/** 用于abandon传参 */
export const bookingFlowIdState = atomWithCompatibleSessionStorage<string>('bookingFlowId', '');

export const bookingReferrerSourceState = atomWithReset<string>('');

export const fromBookAgainState = atomWithReset<boolean>(false);

/**
 * 语法糖,辅助处理 bookingFlowId
 */
// function atomWithBookingFlow<T = any, D = any>(
//   baseAtom: WritableAtom<BookingDataWithStorage<T>, BookingDataWithStorage<T>, any>,
//   defaultValue: () => D,
// ) {
//   const derivedAtom = atom(
//     (get) => {
//       const storageId = get(bookingStorageIdState);
//       console.log('storageId', storageId);
//       const selectedPetIdsMap = get(baseAtom);
//       return selectedPetIdsMap[storageId] ?? defaultValue();
//     },
//     (get, set, newValue: T | typeof RESET) => {
//       const storageId = get(bookingStorageIdState);
//       const selectedPetIdsMap = get(baseAtom);
//       set(
//         baseAtom,
//         newValue === RESET
//           ? {}
//           : {
//               ...selectedPetIdsMap,
//               [storageId]: newValue,
//             },
//       );
//     },
//   );
//   return derivedAtom;
// }

type BookingPetIdsType = number[];

export const bookingPetIdsState = atomWithReset<BookingPetIdsType>([]);

export const bookingPetDataListState = atom<PetInfoInterface[]>((get) => {
  const selectedPetIds = get(bookingPetIdsState);
  const PetStore = get(petStoreAtom);
  const res = PetStore.getList(selectedPetIds);
  return res;
});

export const isEnableSlotFlowState = atom((get) => {
  const isBySlot = get(businessInfoState).data?.setting?.availableTimeType === AvailabilityType.BySlot;
  return isBySlot;
});

export const availableTimesState = atom<AvailableTimesEntity>();
