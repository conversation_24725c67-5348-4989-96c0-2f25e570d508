import { actionAtom } from '@moego/client-lib-jotai/dist/action';
import { extractGoogleAdsQuery } from './googleAds/action';
import { extractMetaAdsQuery } from './metaAds/action';
import { extractUtmQuery } from './utmParams/action';

export interface ExtractQueryParams {
  query: string | URLSearchParams;
}

export const extractAdsQuery = actionAtom((get, set, { query }: ExtractQueryParams) => {
  set(extractGoogleAdsQuery, { query });
  set(extractUtmQuery, { query });
  set(extractMetaAdsQuery, { query });
});
