import { actionAtom } from '@moego/client-lib-jotai/dist/action';
import { type ExtractQueryParams } from '../action';
import { metaAdsState, META_ADS_QUERY_KEY_LIST, type MetAdsState } from './state';

export const extractMetaAdsQuery = actionAtom((get, set, { query }: ExtractQueryParams) => {
  const parsedQuery = typeof query === 'string' ? new URLSearchParams(query) : query;
  const queryRecord = META_ADS_QUERY_KEY_LIST.reduce<MetAdsState>((acc, key) => {
    if (parsedQuery.has(key)) {
      acc[key] = parsedQuery.get(key)!;
    }
    return acc;
  }, {});
  set(metaAdsState, queryRecord);
});

export const clearMetaAdsQuery = actionAtom((get, set) => {
  set(metaAdsState, {});
});
