import { useSetAtom } from 'jotai';
import { memo, useEffect } from 'react';
import { extractAdsQuery } from 'state/ads/action';

export const useSetAdsData = () => {
  const getAdsQuery = useSetAtom(extractAdsQuery);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    getAdsQuery({ query: searchParams });
  }, [getAdsQuery, window.location.search]);
};

export const SetAdsData = memo(() => {
  useSetAdsData();
  return null;
});
