import { useAtomValue } from 'jotai';
import { GoogleAdsQueryKey } from '../googleAds/state';
import { adsState } from '../state';

export const useUTMAndGoogleAdsData = () => {
  const { utmParams, googleAdsParams } = useAtomValue(adsState);
  const gclid = googleAdsParams?.[GoogleAdsQueryKey.GCLID]; // 获取 gclid 参数
  const _gcl_au = googleAdsParams?.[GoogleAdsQueryKey.GCL_AU];
  const gbraid = googleAdsParams?.[GoogleAdsQueryKey.GBRAID];

  return { utmParams, gclid, _gcl_au, gbraid };
};
