import { atom } from 'jotai';
import { googleAdsState } from './googleAds/state';
import { metaAdsState } from './metaAds/state';
import { utmParamsState } from './utmParams/state';

export const adsState = atom((get) => {
  const googleAdsParams = get(googleAdsState);
  const utmParams = get(utmParamsState);
  const metaAdsParams = get(metaAdsState);
  return {
    googleAdsParams,
    utmParams,
    metaAdsParams,
  };
});
