import { actionAtom } from '@moego/client-lib-jotai/dist/action';
import { type ExtractQueryParams } from '../action';
import { extractQueryByPrefix } from '../utils';
import {
  GoogleAdsQueryKey,
  googleAdsState,
  GOOGLE_ADS_QUERY_KEY_LIST,
  GOOGLE_ADS_QUERY_KEY_PREFIX_LIST,
  type GoogleAdsQueryProperty,
  type GoogleAdsState,
} from './state';
/**
 * @fileoverview 一个用于解析谷歌 `_gl` 跨域名链接器参数的工具函数。
 *
 * ===================================================================================
 * ## 技术背景
 * ===================================================================================
 * **问题阐述：** 浏览器的“同源策略”（Same-Origin Policy）禁止一个域名下的脚本和Cookie
 * 访问另一个域名下的数据。在Web分析和广告领域，这项策略会导致“会话碎片化”
 * （Session Fragmentation）和“转化归因中断”（Conversion Attribution Failure），
 * 特别是对于那些需要跨越多个域名（例如，从品牌官网 `a.com` 到支付网关 `b.com`）
 * 的用户旅程。
 *
 * **解决方案：** Google Analytics 的“跨域名链接器”机制通过在URL中传递客户端状态
 * （主要是客户端ID），来解决上述问题。
 *
 * **`_gl` 参数详解：** `_gl` 参数就是实现这种状态传递的标准载体。为了避免用大量
 * 独立的参数污染URL，谷歌将多个键值对数据点**序列化（serialize）**成一个单一的、
 * 由星号（*）分隔的字符串，作为 `_gl` 参数的值。
 *
 * **序列化格式：** 其协议规定字段由星号（*）分隔。一个典型的值如下所示：
 * `1*linker_id*_ga*client_id_value*_gcl_au*gcl_au_value`
 *
 * **本函数目的：** 本函数旨在提供一个健壮（robust）的方法，用于解析这种特定的、由
 * 星号分隔的格式，从而提取指定键的值。它避免了手动操作字符串可能导致的错误。
 *
 *
 * ===================================================================================
 *
 * @param query - `URLSearchParams` 对象，代表URL的查询字符串部分。
 * @param keys - 一个字符串数组，指定需要从 `_gl` 参数值中提取的键名。
 * @returns 一个 `Record<string, string>` 对象，包含所有成功找到并提取出的键值对。
 */
function extractGoogleAdsQueryFromGL(
  query: URLSearchParams,
  keys: readonly GoogleAdsQueryProperty[],
): Record<GoogleAdsQueryProperty, string> {
  // 初始化一个 Record 对象，用于存储最终提取出的键值对。
  const foundParams: Record<string, string> = {};

  // 1. 从URL查询中检索序列化后的 `_gl` 链接器参数值。
  const glValue = query.get('_gl');

  // 2. 如果 `_gl` 参数不存在或为空，则无需解析，直接返回。
  if (!glValue) {
    return foundParams;
  }

  // 3. 根据星号分隔符，将 `_gl` 字符串“反序列化”（de-serialize）为一个部分数组。
  //    这个数组将包含以键、值、键、值...顺序交错的元素。
  const parts = glValue.split('*');

  // 4. 遍历调用者指定需要提取的键（key）的列表。
  for (const keyToFind of keys) {
    // 5. 在反序列化后的数组中定位当前键的索引。
    const keyIndex = parts.indexOf(keyToFind);

    // 6. 校验键是否存在 (`keyIndex !== -1`)，并且其后至少还跟随一个元素作为其值。
    //    这可以防止在 `_gl` 值格式不规范时发生数组越界（out-of-bounds）错误。
    if (keyIndex !== -1 && keyIndex + 1 < parts.length) {
      // 7. 根据协议，值就是数组中紧跟在键后面的那个元素。
      const value = parts[keyIndex + 1];

      // 8. 将找到的键值对存入结果对象。
      foundParams[keyToFind] = value;
    }
  }

  // 9. 返回包含所有成功提取参数的 Record 对象。

  return foundParams;
}

export const normalizeGbraidKey = (params: GoogleAdsState) => {
  const result = { ...params };
  // 将 _gbraid 与 GBRAID 转化成 gbraid
  if (!result[GoogleAdsQueryKey.GBRAID]) {
    if (result[GoogleAdsQueryKey.GBRAID_UNDERSCORE]) {
      result[GoogleAdsQueryKey.GBRAID] = result[GoogleAdsQueryKey.GBRAID_UNDERSCORE];
    } else if (result[GoogleAdsQueryKey.GBRAID_UPPER]) {
      result[GoogleAdsQueryKey.GBRAID] = result[GoogleAdsQueryKey.GBRAID_UPPER];
    }
  }
  return result;
};

export const extractGoogleAdsQuery = actionAtom((_get, set, { query }: ExtractQueryParams) => {
  const parsedQuery = typeof query === 'string' ? new URLSearchParams(query) : query;
  const glQueryRecord = extractGoogleAdsQueryFromGL(parsedQuery, GOOGLE_ADS_QUERY_KEY_LIST);
  const queryRecord = GOOGLE_ADS_QUERY_KEY_LIST.reduce<GoogleAdsState>((acc, key) => {
    if (parsedQuery.has(key)) {
      acc[key] = parsedQuery.get(key)!;
    }
    return acc;
  }, {});

  const prefixQueryRecord = extractQueryByPrefix(parsedQuery, GOOGLE_ADS_QUERY_KEY_PREFIX_LIST);
  // 优先使用直接解析的内容，后备使用反序列化的内容，这里的顺序也许可以调整，但影响不大
  const mergedQueryRecord = { ...queryRecord, ...prefixQueryRecord, ...glQueryRecord };
  const normalizedGbraidQueryRecord = normalizeGbraidKey(mergedQueryRecord);
  set(googleAdsState, normalizedGbraidQueryRecord);
});

export const clearGoogleAdsQuery = actionAtom((_get, set) => {
  set(googleAdsState, {});
});
