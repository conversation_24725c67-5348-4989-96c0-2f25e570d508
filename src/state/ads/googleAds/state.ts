import { atomWithReset } from 'jotai/utils';

export enum GoogleAdsQueryKey {
  // 明文（未加密）,传统 Google Ads 点击（逐步淘汰）
  GCLID = 'gclid',
  // Base64 编码 + 哈希	默认的 GA4 自动关联参数（现代 Web 应用、SPA）
  GCL_AU = '_gcl_au',
  // 跨域加密参数, 配合 _gcl_au 使用,解决跨域跟踪问题
  GL = '_gl',
  // 加密（App 到 Web 归因）	Google Ads 应用广告（App 内跳转至网页）
  GBRAID = 'gbraid',
  // 加密（App 到 Web 归因）	Google Ads 应用广告（App 内跳转至网页）
  GBRAID_UPPER = 'GBRAID',
  // 加密（App 到 Web 归因）	Google Ads 应用广告（App 内跳转至网页）
  GBRAID_UNDERSCORE = '_gbraid',
}

export const GOOGLE_ADS_QUERY_KEY_LIST = Object.values(GoogleAdsQueryKey);

export const GOOGLE_ADS_QUERY_KEY_PREFIX_LIST = [
  // hsa_ 前缀：关联本地服务广告
  'hsa_',
  // gad_ 前缀：用于聚合广告追踪的URL参数
  'gad_',
] as const;

export type GoogleAdsPrefixedKey = `${(typeof GOOGLE_ADS_QUERY_KEY_PREFIX_LIST)[number]}${string}`;

export type GoogleAdsQueryProperty = GoogleAdsQueryKey | GoogleAdsPrefixedKey;
export type GoogleAdsState = Partial<Record<GoogleAdsQueryKey, string>>;

export const googleAdsState = atomWithReset<GoogleAdsState>({});
