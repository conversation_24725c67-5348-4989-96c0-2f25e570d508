import { actionAtom } from '@moego/client-lib-jotai/dist/action';
import { type ExtractQueryParams } from '../action';
import { extractQueryByPrefix } from '../utils';
import { utmParamsState, UTM_KEY_PREFIX } from './state';

export const extractUtmQuery = actionAtom((get, set, { query }: ExtractQueryParams) => {
  const parsedQuery = typeof query === 'string' ? new URLSearchParams(query) : query;
  const queryRecord = extractQueryByPrefix(parsedQuery, [UTM_KEY_PREFIX]);
  set(utmParamsState, queryRecord);
});

export const clearUtmQuery = actionAtom((get, set) => {
  set(utmParamsState, {});
});
