import {
  type BoardingAddon,
  type DaycareAddon,
  type GroomingAddon,
  type Service,
  type ServiceGrooming,
} from '@moego/api-web/moego/client/online_booking/v1/booking_request_api';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { DateType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import dayjs from 'dayjs';
import { omit, sortBy, uniqueId } from 'lodash';
import { type BookingSubmitPetParams } from 'pages/PersonalInfo/hooks/types';
import { type PetInfoInterface } from 'petFactory/petBasic';
import { dateMessageToStandardString, stringToDateMessage } from 'utils/date';
import { DateTypeUtils } from './dateType.utils';
import { dateType2LegacyDateType } from './hooks/legacyDateType.utils';
import { type PetServiceAddOnDetail, type PetServiceDetailItem } from './serviceAddOnDetailAtom';
import { type CommonAddonWithDetail } from './serviceAddOnDetailAtom.types';

/**
 * 合并后的 PetServiceDetailItem，在前端使用；
 * 主要是针对多个 DatePoint 的处理，后端需要独立多个 service detail item，前端是一个 serviceId 对应多个日期；
 */
export interface MergedPetServiceDetailItem extends Omit<PetServiceDetailItem, 'id' | 'serviceId' | 'startDate'> {
  startDateList?: string[];
}

export const generateServiceDetail = (
  serviceId: string,
  inputServiceDetail?: Omit<PetServiceDetailItem, 'id' | 'serviceId'>,
) => {
  const isDatePoint = DateTypeUtils.isDatePointList(inputServiceDetail?.dateType);
  const result: PetServiceDetailItem = {
    id: uniqueId(),
    serviceId,
    dateType: isDatePoint ? DateType.DATE_POINT : DateType.EVERY_DAY_EXCEPT_CHECKOUT_DAY,
    specificDates: [],
    startDate: '',
    quantityPerDay: DefaultQuantityPerDay,
    ...inputServiceDetail,
  };
  return result;
};

/**
 * 如果存在多个 startDate 的话，需要生成多条带 startDate 的 serviceDetail 记录
 */
export const generateServiceDetailList = (
  serviceId: string,
  serviceUserInput?: MergedPetServiceDetailItem,
): PetServiceDetailItem[] => {
  const startDateList = serviceUserInput?.startDateList || [];
  const isDatePointList = DateTypeUtils.isDatePointList(serviceUserInput?.dateType);
  const serviceDetailInputParam = omit(
    {
      ...serviceUserInput,
    },
    'startDateList',
  );
  const currentServiceDetail = generateServiceDetail(serviceId, serviceDetailInputParam);
  return isDatePointList
    ? startDateList.map((startDate) => ({
        ...currentServiceDetail,
        id: uniqueId(),
        startDate,
      }))
    : [currentServiceDetail];
};

export const mergeServiceDetailListIntoMap = (serviceDetailList: PetServiceDetailItem[]) => {
  const mergedServiceDetailList: PetServiceDetailItem[] = [];
  const serviceDetailMap = new Map<string, MergedPetServiceDetailItem>();
  serviceDetailList.forEach((serviceDetail) => {
    const isDatePointList = DateTypeUtils.isDatePointList(serviceDetail.dateType);
    if (serviceDetailMap.has(serviceDetail.serviceId)) {
      const existingServiceDetail = serviceDetailMap.get(serviceDetail.serviceId);
      if (existingServiceDetail) {
        if (isDatePointList) {
          serviceDetailMap.set(serviceDetail.serviceId, {
            ...serviceDetail,
            startDateList: [
              ...new Set([
                ...(existingServiceDetail.startDateList || []),
                ...(serviceDetail.startDate ? [serviceDetail.startDate] : []),
              ]),
            ],
          });
          return;
        }
        serviceDetailMap.set(serviceDetail.serviceId, existingServiceDetail);
      }
    } else {
      serviceDetailMap.set(
        serviceDetail.serviceId,
        isDatePointList
          ? {
              ...serviceDetail,
              startDateList: serviceDetail.startDate ? [serviceDetail.startDate] : [],
            }
          : serviceDetail,
      );
      mergedServiceDetailList.push(serviceDetail);
    }
  });
  return Array.from(serviceDetailMap.entries()).reduce((acc, [serviceId, mergedServiceDetail]) => {
    acc[serviceId] = mergedServiceDetail;
    return acc;
  }, {} as { [serviceId: string]: MergedPetServiceDetailItem });
};

export const getEmptyPetServiceAddOnDetail = (petKey: number): PetServiceAddOnDetail => ({
  petKey,
  selectedServiceInfoList: [],
  selectedAddOnInfoList: [],
  serviceDetailList: [],
  addOnDetailList: [],
  waitListServiceInfoList: [],
});

export const getBoardingAddOns = (addOnDetailList: CommonAddonWithDetail[]): BoardingAddon[] => {
  return addOnDetailList.map((item) => {
    const {
      serviceId,
      specificDates,
      servicePrice,
      serviceTaxId,
      serviceDuration,
      quantityPerDay,
      dateType,
      startDate,
    } = item;

    return {
      id: serviceId,
      servicePrice: servicePrice || 0,
      taxId: serviceTaxId || '',
      duration: serviceDuration || 0,
      // 后端接口依然用的 PetDetailDateType，这里需要做一层转换
      dateType: dateType ? dateType2LegacyDateType(dateType) : undefined,
      dates: specificDates || [],
      startDate: startDate ? stringToDateMessage(startDate) : undefined,
      quantityPerDay: quantityPerDay || DefaultQuantityPerDay,
    };
  });
};

export const getDaycareAddOns = (addOnDetailList: CommonAddonWithDetail[]): DaycareAddon[] => {
  return addOnDetailList.map((item) => {
    const {
      serviceId,
      specificDates,
      servicePrice,
      serviceTaxId,
      serviceDuration,
      quantityPerDay,
      dateType,
      startDate,
    } = item;
    const isDatePoint = DateTypeUtils.isDaycareDatePoint(dateType);
    const isEveryDay = DateTypeUtils.isDaycareEveryDay(dateType);
    return {
      id: serviceId,
      servicePrice: servicePrice || 0,
      taxId: serviceTaxId || '',
      duration: serviceDuration || 0,
      // 注意：daycare 后端还是用的 isEveryDay + dates 的模型，不涉及 dateType
      isEveryDay,
      dates: isDatePoint && startDate ? [startDate] : specificDates || [],
      quantityPerDay: quantityPerDay || DefaultQuantityPerDay,
    };
  });
};

export const getGroomingAddOns = (addOnDetailList: CommonAddonWithDetail[]): GroomingAddon[] => {
  return addOnDetailList.map((item) => {
    const { serviceId } = item;
    return {
      id: serviceId,
    };
  });
};

export const DefaultQuantityPerDay = 1;

/**
 * 根据 BD Submit 接口，生成 BookingSubmitPetParams，给老的 amount 相关接口传参
 */
export const submitServiceToBookingSubmitPetParams = (
  service: Service,
  pet: PetInfoInterface,
  groupClassId?: string,
  referenceMainService?: Service,
) => {
  const resultPetParams: BookingSubmitPetParams[] = [];
  const basicPetInfo = omit(pet, ['key', 'isNewPet', 'unavailableReasons', 'evaluationStatus']);
  const { boarding, daycare, grooming, groupClass } = service;

  if (groupClass && groupClassId) {
    const petInfo: BookingSubmitPetParams = {
      ...basicPetInfo,
      serviceId: +groupClassId,
      isSelected: true,
    };
    resultPetParams.push(petInfo);
  }
  if (boarding) {
    const petInfo: BookingSubmitPetParams = {
      ...basicPetInfo,
      startDate: boarding?.startDate,
      endDate: boarding?.endDate,
      serviceId: boarding?.serviceId ? +boarding.serviceId : undefined,
      addons:
        boarding?.addons?.map((item) => ({
          id: +item.id,
          dates: item.dates,
          // Boarding 的 isEveryDay 被弃用，用 dateType 替代，这里只做兜底
          // TODO: 6/6 dateType 改造，需要关注废弃掉这个字段
          isEveryDay: !!item.dateType && +item.dateType === DateType.EVERY_DAY_EXCEPT_CHECKOUT_DAY,
          quantityPerDay: item.quantityPerDay || DefaultQuantityPerDay,
          dateType: item.dateType || DateType.UNSPECIFIED,
          startDate: item.startDate ? dateMessageToStandardString(item.startDate) : '',
        })) || [],
      isSelected: true,
    };
    resultPetParams.push(petInfo);
  }
  if (daycare) {
    const dates = daycare?.dates || [];
    dates.forEach((date) => {
      const petInfo: BookingSubmitPetParams = {
        ...basicPetInfo,
        startDate: date,
        serviceId: daycare?.serviceId ? +daycare.serviceId : undefined,
        addons:
          (daycare?.addons?.map((item) => ({
            id: +item.id,
            // Multi Day Daycare 的 addOn date 只有两种模式：everyDay 和 specificDates
            isEveryDay: item.isEveryDay,
            dates: item.dates,
            quantityPerDay: item.quantityPerDay || DefaultQuantityPerDay,
          })) as BookingSubmitPetParams['addons']) || [],
        isSelected: true,
      };
      resultPetParams.push(petInfo);
    });
  }
  if (grooming) {
    const fallbackStartDate = getFallbackStartDateForGroomingService(grooming, referenceMainService);
    const startDate = grooming?.startDate ?? fallbackStartDate;
    const petInfo: BookingSubmitPetParams = {
      ...basicPetInfo,
      startDate,
      serviceId: grooming?.serviceId ? +grooming.serviceId : undefined,
      addons: grooming?.addons?.map((item) => ({
        id: +item.id,
        /**
         * From gq，关于为什么下面字段为固定值：
         * grooming addOn 实际上给后台只传 id，UI 上只有 bundled add-on 这种 case
         * 所以纯前端角度填默认值就可以，如果 UI 有处理这种交互的话，扩展的时候再搞也 OK 的
         */
        dates: [],
        isEveryDay: false,
        quantityPerDay: DefaultQuantityPerDay,
        dateType: DateType.DATE_POINT,
        startDate: startDate as string,
      })),
      isSelected: true,
    };
    resultPetParams.push(petInfo);
  }

  return resultPetParams;
};

const getMainServiceStartDateEndDate = (service?: Service) => {
  if (!service) {
    return;
  }

  const { boarding, daycare } = service;
  if (boarding) {
    return {
      startDate: boarding.startDate,
      endDate: boarding.endDate,
    };
  }
  if (daycare) {
    const dates = daycare?.dates || [];
    const sortedDates = sortBy(dates, (date) => dayjs(date).valueOf());
    return {
      startDate: sortedDates[0],
      endDate: sortedDates[sortedDates.length - 1],
    };
  }
};

/**
 * 只有在 boarding flow 下 groomingService 本身的 dateType 是 FirstDay 和 LastDay 的时候才需要根据 mainService 补充 startDate
 * 其他情况下它本身需要 startDate 就会自带有值的 startDate 字段
 */
const getFallbackStartDateForGroomingService = (groomingService: ServiceGrooming, referenceMainService?: Service) => {
  const shouldHaveFallbackStartDate =
    groomingService &&
    referenceMainService?.boarding &&
    groomingService?.dateType &&
    [PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY, PetDetailDateType.PET_DETAIL_DATE_LAST_DAY].includes(
      groomingService?.dateType,
    );

  if (!shouldHaveFallbackStartDate) {
    return;
  }

  const { startDate: mainServiceStartDate, endDate: mainServiceEndDate } =
    getMainServiceStartDateEndDate(referenceMainService) || {};
  const currentDateType = groomingService.dateType;
  const fallbackStartDate =
    currentDateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY
      ? mainServiceStartDate
      : currentDateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY
      ? mainServiceEndDate
      : undefined;
  return fallbackStartDate;
};
