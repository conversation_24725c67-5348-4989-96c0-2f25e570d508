import {
  AppointmentCardType,
  ListAppointmentsParamsAppointmentSortField,
  type GetAppointmentDetailParams,
  type GetAppointmentDetailResult,
  type IsAvailableForRescheduleParams,
  type ListAppointmentsParams,
  type ListAppointmentsParamsAppointmentType,
  type ReschedulePetFeedingMedicationParamsPetScheduleDef,
} from '@moego/api-web/moego/client/online_booking/v1/appointment_api';
import { ServiceType } from '@moego/api-web/moego/models/grooming/v1/service_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { actionAtom } from '@moego/client-lib-jotai/action';
import { loadable, loadableWithReset } from '@moego/client-lib-jotai/dist/loadable';
import { type PartialRequired } from '@moego/client-lib-types/common';
import { getSearchParam } from '@moego/client-lib-utils/dist/query';
import { atom } from 'jotai';
import { atomWithReset } from 'jotai/utils';
import { http } from 'libs/api';
import { AppointmentAPIClient, AppointmentServiceClient, BookingRequestServiceClient } from 'libs/clients';
import { omit } from 'lodash';
import {
  extractServiceDetailKey,
  getApptServiceDetailKey,
} from 'pages/BoardingDaycare/AppointmentDetail/modules/AppointmentDetailEdit/AppointmentDetailEdit.utils';
import { customerIdState, didLoginState } from 'state/account/state';
import { bookingAddressExtraInfoState } from 'state/booking/state';
import { bookingNamePayload } from 'state/business/state';
import { AppointmentTypeId } from 'types/appointment';
import { type OpenApiModels } from 'types/openApi/schema';

export interface FetchAppointmentListParams extends Pick<ListAppointmentsParams, 'pagination'> {
  apptTypeId: AppointmentTypeId;
}

const AppointmentListTypeSort = {
  [AppointmentTypeId.Pending]: true,
  [AppointmentTypeId.Upcoming]: true,
  [AppointmentTypeId.Cancelled]: false,
  [AppointmentTypeId.Past]: false,
};

export const fetchAppointmentListAction = atom((get) => async (params: FetchAppointmentListParams) => {
  const { apptTypeId, pagination } = params;
  const payload: ListAppointmentsParams = {
    ...get(bookingNamePayload),
    filter: { appointmentType: apptTypeId as unknown as ListAppointmentsParamsAppointmentType },
    pagination,
    sorts: [
      {
        field: ListAppointmentsParamsAppointmentSortField.DATE_TIME,
        asc: AppointmentListTypeSort[apptTypeId],
      },
    ],
  };
  return AppointmentServiceClient.listAppointments(payload);
});

const fetchCurrentAppointmentDetail = async (appointmentId?: string, bookingRequestId?: string) => {
  const params = {
    ...(appointmentId ? { appointmentId } : undefined),
    ...(bookingRequestId ? { bookingRequestId } : undefined),
  };
  return await AppointmentServiceClient.getAppointmentDetail(params);
};

export const refreshedBDAppointmentDetailAtom = atomWithReset<GetAppointmentDetailResult>(
  {} as GetAppointmentDetailResult,
);

export const refreshBDAppointmentDetailAction = actionAtom(async (get, set) => {
  const { appointmentId, bookingRequestId } = get(bdAppointmentDetailParamsWithSearchAtom);
  set(refreshedBDAppointmentDetailAtom, await fetchCurrentAppointmentDetail(appointmentId, bookingRequestId));
});

export const bdAppointmentDetailParamsAtom = atom({
  appointmentId: '',
  bookingRequestId: '',
});

export const bdAppointmentDetailParamsWithSearchAtom = atom((get) => {
  let { appointmentId, bookingRequestId } = get(bdAppointmentDetailParamsAtom);
  // url 拿到的 params 会有延迟，所以咱以 store 为准，首次加载 store 没有值，再从 url 里面取
  if (!appointmentId && !bookingRequestId) {
    appointmentId = getSearchParam('apptId') || '';
    bookingRequestId = getSearchParam('bookingRequestId') || '';
  }
  return {
    appointmentId,
    bookingRequestId,
  };
});

export const bdAppointmentDetailLoadableAtom = loadableWithReset(
  () =>
    atom(async (get) => {
      const { appointmentId, bookingRequestId } = get(bdAppointmentDetailParamsWithSearchAtom);
      // 如果依赖的 atom 刷新了，就直接用刷新后的数据，实现不触发 loading 的刷新能力
      const refreshedData = get(refreshedBDAppointmentDetailAtom);
      if (refreshedData?.appointment) {
        return refreshedData;
      }
      return await fetchCurrentAppointmentDetail(appointmentId, bookingRequestId);
    }),
  { throwError: true },
);

/**
 * 当前是否在 Appointment Detail 的上下文，部分 Atom 不好改造，会依赖
 * TODO: 后续需要下掉，避免 AppointmentDetail 和 Booking Flow 耦合
 */
export const isInAppointmentDetailAtom = atom(false);

export const bdAppointmentDetailMainCareTypeAtom = atom((get) => {
  const { appointment } = get(bdAppointmentDetailLoadableAtom).data || {};
  return appointment?.mainCareType || ServiceItemType.BOARDING;
});

export const reschedulePetFeedingMedicationAction = actionAtom(
  async (get, set, schedules?: ReschedulePetFeedingMedicationParamsPetScheduleDef[]) => {
    const { appointmentId, bookingRequestId } = get(bdAppointmentDetailParamsWithSearchAtom);
    const params = {
      ...(appointmentId ? { appointmentId } : undefined),
      ...(bookingRequestId ? { bookingRequestId } : undefined),
      schedules: schedules ?? [],
    };
    return await AppointmentServiceClient.reschedulePetFeedingMedication(params);
  },
);

export const bdPriorityAppointmentAtom = loadable(
  atom(async (get) => {
    const isExistClient = get(didLoginState);
    if (!isExistClient) {
      return {};
    }

    const { domain, name } = get(bookingNamePayload) || {};
    const { count, card } = await AppointmentServiceClient.getPriorityAppointmentCard({ domain, name });
    const { cardType, appointment, petAndServices } = card || {};
    const isPending = cardType === AppointmentCardType.PENDING;
    const isInProgress = cardType === AppointmentCardType.IN_PROGRESS;

    return {
      count: isPending || isInProgress ? count : undefined,
      cardType,
      appointment,
      petAndServices,
    };
  }),
  { throwError: true },
);

export const cancelAppointment = actionAtom(async (get, set, input: GetAppointmentDetailParams) => {
  await AppointmentServiceClient.cancelAppointment(input);
});

export const updateAppointmentAction = actionAtom(
  async (get, set, input: { petAndServices: any[]; removedServiceDetailKeys?: string[] }) => {
    const { appointmentId } = get(bdAppointmentDetailParamsWithSearchAtom);
    if (!appointmentId) {
      throw new Error('No appointment ID found');
    }
    const { removedServiceDetailKeys = [], petAndServices } = input;

    // Convert petAndServices to petDetails format expected by AppointmentAPIClient
    // petDetails should be an array of UpdateAppointmentRequestPetDetail
    const petDetails: any[] = [];

    // Handle removed services first - use delete operation
    removedServiceDetailKeys.forEach((serviceDetailKey) => {
      const { petDetailId } = extractServiceDetailKey(serviceDetailKey);
      petDetails.push({
        delete: {
          petDetailId,
        },
      });
    });

    // Handle existing services that need to be updated
    petAndServices.forEach((petService) => {
      const { services = [], addOns = [], petId } = petService;

      // Handle main services
      services.forEach((service: any) => {
        // Check if this service should be removed
        const serviceDetailKey = getApptServiceDetailKey(
          petId,
          service.petDetailId,
          service.careType,
          ServiceType.SERVICE,
        );
        if (removedServiceDetailKeys.includes(serviceDetailKey)) {
          return; // Skip this service as it should be deleted
        }

        if (service.petDetailId) {
          // Existing service - update using nested petDetail structure
          petDetails.push({
            update: {
              petDetail: {
                id: service.petDetailId,
                groomingId: String(appointmentId),
                petId: String(petId),
                serviceId: service.serviceId,
                serviceItemType: service.careType,
                startTime: service.startTime,
                endTime: service.endTime,
                startDate: service.startDate,
                endDate: service.endDate,
                specificDates: service.specificDates?.join(',') || '', // Convert array to comma-separated string
              },
            },
          });
        } else {
          // New service - add
          petDetails.push({
            add: {
              petDetail: {
                groomingId: String(appointmentId),
                petId: String(petId),
                serviceId: service.serviceId,
                serviceItemType: service.careType,
                startTime: service.startTime,
                endTime: service.endTime,
                startDate: service.startDate,
                endDate: service.endDate,
                specificDates: service.specificDates || [],
              },
            },
          });
        }
      });

      // Handle add-ons
      addOns.forEach((addOn: any) => {
        // Check if this add-on should be removed
        const addOnDetailKey = getApptServiceDetailKey(petId, addOn.petDetailId, addOn.careType, ServiceType.ADD_ONS);
        if (removedServiceDetailKeys.includes(addOnDetailKey)) {
          return; // Skip this add-on as it should be deleted
        }

        if (addOn.petDetailId) {
          // Existing add-on - update using nested petDetail structure
          petDetails.push({
            update: {
              petDetail: {
                id: addOn.petDetailId,
                groomingId: String(appointmentId),
                petId: String(petId),
                serviceId: addOn.serviceId,
                serviceItemType: addOn.associatedServiceCareType,
                associatedServiceId: addOn.associatedServiceId,
                specificDates: addOn.specificDates?.join(',') || '', // Convert array to comma-separated string
                // Note: quantityPerDay might not be a field in UpdatePetDetailRequest
                // TODO: Check if this field exists in the interface
              },
            },
          });
        } else {
          // New add-on - add
          petDetails.push({
            add: {
              petDetail: {
                groomingId: String(appointmentId),
                petId: String(petId),
                serviceId: addOn.serviceId,
                serviceItemType: addOn.associatedServiceCareType,
                associatedServiceId: addOn.associatedServiceId,
                specificDates: addOn.specificDates || [],
                quantityPerDay: addOn.quantityPerDay,
              },
            },
          });
        }
      });
    });

    const params = {
      appointmentId,
      petDetails,
    };
    await AppointmentAPIClient.updateAppointment(params);
  },
);

export const updateBookingRequestAction = actionAtom(
  async (get, set, input: { petAndServices: any[]; removedServiceDetailKeys?: string[] }) => {
    const { bookingRequestId } = get(bdAppointmentDetailParamsWithSearchAtom);
    if (!bookingRequestId) {
      throw new Error('No booking request ID found');
    }
    const { removedServiceDetailKeys = [], petAndServices } = input;

    // Convert petAndServices to serviceDetails format expected by BookingRequestServiceClient
    const serviceDetails: any[] = [];

    // Add delete operations for removed services
    removedServiceDetailKeys.forEach((serviceDetailKey) => {
      // Extract petDetailId from serviceDetailKey for booking request deletion
      const { petDetailId } = extractServiceDetailKey(serviceDetailKey);
      serviceDetails.push({
        delete: {
          id: petDetailId,
        },
      });
    });

    // Add update operations for existing services
    petAndServices.forEach((petService) => {
      const { services = [], petId } = petService;

      // Handle main services
      services.forEach((service: any) => {
        if (service.petDetailId) {
          // Existing service - update
          const updateService: any = {};

          // Determine service type and structure accordingly
          if (service.careType === ServiceItemType.BOARDING) {
            updateService.boarding = {
              service: { id: service.petDetailId },
              addons: [],
              addonsV2: [], // Add-ons will be handled separately
            };
          } else if (service.careType === ServiceItemType.DAYCARE) {
            updateService.daycare = {
              service: { id: service.petDetailId },
              addons: [],
              addonsV2: [], // Add-ons will be handled separately
            };
          } else if (service.careType === ServiceItemType.GROOMING) {
            updateService.grooming = {
              service: { id: service.petDetailId },
              addons: [],
            };
          }

          serviceDetails.push({
            update: updateService,
          });
        } else {
          // New service - add
          const addService: any = {};

          // Determine service type and structure accordingly
          if (service.careType === ServiceItemType.BOARDING) {
            addService.boarding = {
              service: {
                petId: String(petId),
                serviceId: service.serviceId,
                startDate: service.startDate,
                endDate: service.endDate,
                startTime: service.startTime,
                endTime: service.endTime,
              },
              addons: [],
              addonsV2: [],
            };
          } else if (service.careType === ServiceItemType.DAYCARE) {
            addService.daycare = {
              service: {
                petId: String(petId),
                serviceId: service.serviceId,
                specificDates: service.specificDates || [],
                dateType: service.dateType,
              },
              addons: [],
              addonsV2: [],
            };
          } else if (service.careType === ServiceItemType.GROOMING) {
            addService.grooming = {
              service: {
                petId: String(petId),
                serviceId: service.serviceId,
                specificDates: service.specificDates || [],
                dateType: service.dateType,
              },
              addons: [],
            };
          }

          serviceDetails.push({
            add: addService,
          });
        }
      });

      // Handle add-ons (they are typically updated as part of their parent service)
      // For now, we'll skip individual add-on handling as they're usually managed
      // through the parent service's addonsV2 field
    });

    const params = {
      bookingRequestId,
      services: [],
      serviceDetails,
    };
    await BookingRequestServiceClient.updateBookingRequest(params);
  },
);

export const checkIsAvailableForRescheduleAction = actionAtom(
  async (
    get,
    set,
    input: PartialRequired<IsAvailableForRescheduleParams, 'serviceItemType' | 'startDate' | 'endDate'>,
  ) => {
    const { appointmentId, bookingRequestId } = get(bdAppointmentDetailParamsWithSearchAtom);
    const params = {
      ...(Boolean(appointmentId) && { appointmentId }),
      ...(Boolean(bookingRequestId) && { bookingRequestId }),
      ...input,
    };
    return await AppointmentServiceClient.isAvailableForReschedule(params);
  },
);

export type FetchGroomingServiceAvailabilityParams = OpenApiModels['POST/grooming/ob/v2/client/timeslot']['Req'];
export type FetchGroomingServiceAvailabilityResponse = OpenApiModels['POST/grooming/ob/v2/client/timeslot']['Res'];
export const fetchGroomingServiceAvailabilityAction = actionAtom(
  async (get, set, input: Partial<FetchGroomingServiceAvailabilityParams>) => {
    const bookingAddressExtraInfo = get(bookingAddressExtraInfoState);
    const params = {
      customerId: get(customerIdState)!,
      ...omit(bookingAddressExtraInfo, 'startDate'),
      ...input,
    };
    return await http.open('POST/grooming/ob/v2/client/timeslot', params);
  },
);

export const allowFreeDaycareAtom = loadable(
  atom(async (_get, _set) => {
    const { appointments: evaluations } = await AppointmentServiceClient.listEvaluations({
      pagination: {
        pageNum: 1,
        pageSize: 500,
      },
    });
    return !evaluations.length;
  }),
  { throwError: true, initial: false },
);
