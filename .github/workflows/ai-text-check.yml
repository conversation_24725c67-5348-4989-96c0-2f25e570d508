name: AI Text Check

on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - "src/**/*.ts"
      - "src/**/*.tsx"

jobs:
  ai-text-check:
    uses: MoeGolibrary/moego-actions-tool/.github/workflows/ai-text-check.yml@production
    with:
      project_name: "moego-online-booking-client-web"
    secrets:
      GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
      MOEGO_AI_API_KEY: ${{ secrets.MOEGO_AI_API_KEY }}
      NPM_PUBLISHER_USR: ${{ vars.NPM_PUBLISHER_USR }}
      NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}
