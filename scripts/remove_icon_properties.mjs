import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const iconsDir = path.join(__dirname, '../src/assets/icons');

export function remove() {
  const icons = fs.readdirSync(iconsDir).filter((file) => file.endsWith('.svg'));

  icons.forEach((icon) => {
    const iconPath = path.join(iconsDir, icon);
    const content = fs.readFileSync(iconPath, 'utf-8');

    const newContent = removeSvgSize(content.replace(/fill=".+?"\s?/g, ''));
    if (content !== newContent) {
      fs.writeFileSync(iconPath, newContent);
    }
  });
}

function removeSvgSize(content) {
  const contents = content.split('\n');
  const index = contents.findIndex((line) => line.includes('<svg'));
  if (index === -1) {
    return content;
  }
  contents[index] = contents[index].replace(/width=".+?"\s?/g, '').replace(/height=".+?"\s?/g, '');
  return contents.join('\n');
}
