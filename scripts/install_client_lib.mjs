import { exec } from 'child_process';
import fs from 'fs/promises';
import yargs from 'yargs';

const argv = yargs(process.argv.slice(2)).options({
  branch: {
    type: 'string',
    default: '',
    description: 'specify grey branch',
  },
}).argv;

const VERSION_CHECK_PACKAGE = '@moego/client-lib-cli';

const getPackageName = () => {
  return argv.branch ? `${VERSION_CHECK_PACKAGE}@${argv.branch}` : VERSION_CHECK_PACKAGE;
};

async function getLatestVersion() {
  const packageName = getPackageName();
  return new Promise((resolve, reject) => {
    exec(`pnpm view ${packageName} version`, (error, stdout) => {
      if (error) {
        console.error(`Error getting version of ${packageName}: ${error}`);
        reject(error);
        return;
      }

      resolve(stdout.trim());
    });
  });
}

async function updateDependencies() {
  const data = await fs.readFile('package.json', 'utf-8');
  const packageJson = JSON.parse(data);

  const dependencies = [
    ...Object.keys(packageJson.dependencies || {}),
    ...Object.keys(packageJson.devDependencies || {}),
  ];

  const moegoDependencies = dependencies.filter((dep) => dep.startsWith('@moego/client-lib-'));
  const latestVersion = await getLatestVersion();

  console.log('Updating dependencies to version', latestVersion);

  for (const dep of moegoDependencies) {
    if (packageJson.dependencies[dep]) {
      packageJson.dependencies[dep] = latestVersion;
    } else if (packageJson.devDependencies[dep]) {
      packageJson.devDependencies[dep] = latestVersion;
    }
  }

  await fs.writeFile('package.json', JSON.stringify(packageJson, null, 2));
  exec('pnpm install --color', (error, stdout) => {
    if (error) {
      console.error(`Error installing dependencies: ${error}`);
      return;
    }

    console.log(stdout);
  });
  exec('prettier --write package.json');
}

updateDependencies();
