import cp from 'child_process';
import * as fs from 'fs';
import * as process from 'node:process';
import * as path from 'path';

async function main() {
  const target = process.argv[2];
  if (!target) {
    throw new Error('No target folder specified');
  }

  const { name } = JSON.parse(fs.readFileSync(path.resolve(target, 'package.json'), 'utf-8'));
  if (name !== 'moego-client-libs') {
    throw new Error('Target folder is not a moego-client-libs folder');
  }

  const deps = JSON.parse(fs.readFileSync('./package.json', 'utf-8')).dependencies;
  const packages = fs
    .readdirSync(path.resolve(target, './packages'))
    .filter((i) => !!deps[`@moego/client-lib-${i}`])
    .map((name) => path.resolve(target, './packages', name));
  packages.forEach((p) => {
    cp.execSync(`pnpm link ${p}`, { stdio: 'inherit' });
  });
}

main().then(() => console.log('Done'));
