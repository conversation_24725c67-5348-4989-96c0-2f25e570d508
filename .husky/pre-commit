#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

GIT_DIR=$(git rev-parse --git-dir)
if [ ! -f "$GIT_DIR/MERGE_HEAD" ] && \
   [ ! -f "$GIT_DIR/REBASE_HEAD" ] && \
   [ ! -f "$GIT_DIR/CHERRY_PICK_HEAD" ] && \
   [ ! -f "$GIT_DIR/REVERT_HEAD" ]; then
  echo "✅ 非 Git 操作状态，执行 lint-staged"
  git -c core.whitespace=-space-before-tab,-trailing-space --no-pager diff --cached --check && npx lint-staged
else
  echo "🚫 Git 正在执行 merge/rebase/cherry-pick/revert，跳过 lint-staged"
fi


